using UnityEngine;
using DG.Tweening;

namespace _Tasks.NewFeature.Settings
{
    [CreateAssetMenu(menuName = "MergeSolitaire/Settings/GameEffects", fileName = "GameEffectsSettings")]
    public class GameEffectsSettings : ScriptableObject
    {
        [Header("Score Feedback")]
        [SerializeField] private ScoreFeedbackConfig _scoreFeedback = new ScoreFeedbackConfig();
        
        [Header("Merge Animation")]
        [SerializeField] private MergeAnimationConfig _mergeAnimation = new MergeAnimationConfig();

        [Header("Cell Spawn")]
        [SerializeField] private CellSpawnConfig _cellSpawn = new CellSpawnConfig();

        [Header("Destination Arrival")]
        [SerializeField] private DestinationArrivalConfig _destinationArrival = new DestinationArrivalConfig();

        public ScoreFeedbackConfig ScoreFeedback => _scoreFeedback;
        public MergeAnimationConfig MergeAnimation => _mergeAnimation;
        public CellSpawnConfig CellSpawn => _cellSpawn;
        public DestinationArrivalConfig DestinationArrival => _destinationArrival;
    }

    [System.Serializable]
    public class ScoreFeedbackConfig
    {
        [Header("Floating Score Text")]
        public float floatingTextSize = 24f;
        public Color floatingTextColor = Color.yellow;
        public float floatingDistance = 2f;
        public float floatingDuration = 1.5f;
        public Ease floatingEase = Ease.OutQuart;
        
        [Header("Score Counter Animation")]
        public float counterAnimationDuration = 0.8f;
        public Ease counterEase = Ease.OutQuart;
        public float counterStepDelay = 0.02f;
        
        [Header("Score Milestone Effects")]
        public float milestoneScaleDuration = 0.5f;
        public float milestoneScaleMultiplier = 1.2f;
        public Color milestoneHighlightColor = Color.yellow;
        public float milestoneGlowDuration = 1f;
        public float milestonePulseDuration = 0.6f;
        
        [Header("Floating Score Movement")]
        public float moveToScoreDuration = 1.2f;
        public Ease moveToScoreEase = Ease.InOutQuart;
        public float arrivalScaleEffect = 1.3f;
        public float arrivalScaleDuration = 0.3f;
    }



    [System.Serializable]
    public class MergeAnimationConfig
    {
        [Header("Scale Effects")]
        public float scaleDownDuration = 0.3f;
        public float scaleUpDuration = 0.4f;
        public Ease scaleEase = Ease.OutBack;
        public float finalScaleMultiplier = 1.1f;

        [Header("Visual Effects")]
        public bool enableParticles = true;
        public bool enableFlash = true;
        public Color flashColor = Color.white;
        public float flashDuration = 0.2f;
    }

    [System.Serializable]
    public class CellSpawnConfig
    {
        [Header("Spawn Animation")]
        public float duration = 0.5f;
        public Ease scaleEase = Ease.OutBack;
        public float delay = 0.05f;
        public bool staggered = true;
        
        [Header("Alpha Effects")]
        public bool enableFadeIn = true;
        public float fadeInDuration = 0.3f;
        public Ease fadeEase = Ease.OutQuart;
    }

    [System.Serializable]
    public class DestinationArrivalConfig
    {
        [Header("Scale Pulse")]
        public bool enableScalePulse = true;
        public float pulseScale = 1.2f;
        public float pulseDuration = 0.3f;
        public Ease pulseEase = Ease.OutElastic;

        [Header("Visual Feedback")]
        public bool enableGlow = true;
        public Color glowColor = Color.cyan;
        public float glowDuration = 0.5f;

        [Header("Impact Scale Effect")]
        public float impactScaleMultiplier = 1.1f;
        public float impactDuration = 0.1f;
    }
}
