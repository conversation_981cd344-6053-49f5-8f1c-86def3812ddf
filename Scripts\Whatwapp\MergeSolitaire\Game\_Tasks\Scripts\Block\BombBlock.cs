using System.Collections;
using System.Collections.Generic;
using _Tasks.Events;
using _Tasks.NewFeature.Controllers;
using _Tasks.NewFeature.Shared;
using UnityEngine;
using Whatwapp.MergeSolitaire.Game;
using Whatwapp.MergeSolitaire.Shared;

namespace _Tasks.NewFeature
{
    public class BombBlock : Block, ISpecialBlock
    {
        [Header("Bomb Settings")]
        [SerializeField] private float _explosionDelay = 0.5f;
        [SerializeField] private int _explosionRadius = 1;
        [SerializeField] private bool _destroysSelf = true;
        [SerializeField] private bool _debugMode = false;

        public BlockType Type => BlockType.Bomb;
        public bool CanMerge => false; // Bombs don't merge

        private bool _hasExploded = false;
        private Coroutine _explosionCoroutine;
        private bool _isPlaced = false;
        private bool _hasMovementStopped = false;

        public override void Init(BlockValue value, BlockSeed seed)
        {
            base.Init(value, seed);

            if (_debugMode)
                Debug.Log($"[BombBlock] Initialized at position {transform.position}");
        }

        public void OnPlacementComplete()
        {
            Debug.Log("[BombBlock] OnPlacementComplete called!"); // Temporary debug log

            _isPlaced = true;
            var currentPos = GetCurrentCellPosition();

            // Broadcast bomb placed event
            var bombPlacedEvent = new BombPlacedEvent
            {
                BombBlock = this,
                Position = currentPos,
                ExplosionDelay = _explosionDelay
            };

            EventManager.Broadcast(bombPlacedEvent);

            // Only start explosion timer if we're not going to move
            // If we're at the bottom row or there's a block below us, we won't move
            if (ShouldStartExplosionTimer())
            {
                StartExplosionTimer();
            }

            if (_debugMode)
                Debug.Log($"[BombBlock] Placed at position {currentPos}, will move: {!ShouldStartExplosionTimer()}");
        }

        public void OnMovementStopped()
        {
            Debug.Log("[BombBlock] OnMovementStopped called!"); // Temporary debug log

            if (_hasExploded) return;

            _hasMovementStopped = true;
            var currentPos = GetCurrentCellPosition();
            var affectedCells = GetAffectedCells();

            // Broadcast movement stopped event
            var movementStoppedEvent = new BombMovementStoppedEvent
            {
                BombBlock = this,
                FinalPosition = currentPos,
                AffectedCells = affectedCells
            };

            EventManager.Broadcast(movementStoppedEvent);

            // Only start explosion timer if we haven't already started one
            if (_explosionCoroutine == null)
            {
                StartExplosionTimer();
            }

            if (_debugMode)
                Debug.Log($"[BombBlock] Movement stopped at {currentPos}, explosion timer status: {(_explosionCoroutine != null ? "running" : "started")}");
        }

        /// <summary>
        /// Determines if the bomb should start its explosion timer immediately
        /// Returns true if the bomb won't move (is at bottom or has block below)
        /// </summary>
        private bool ShouldStartExplosionTimer()
        {
            if (SpecialBlockController.Instance == null) return true; // Safe fallback

            var currentPos = GetCurrentCellPosition();
            var board = SpecialBlockController.Instance.GetBoard();

            if (board == null) return true; // Safe fallback

            // Check if we're at the bottom row
            if (currentPos.y == 0) return true;

            // Check if there's a block below us
            var cellBelow = board.GetCell(currentPos.x, currentPos.y - 1);
            if (cellBelow != null && !cellBelow.IsEmpty) return true;

            // We will move, so don't start timer yet
            return false;
        }

        /// <summary>
        /// Starts the explosion timer safely
        /// </summary>
        private void StartExplosionTimer()
        {
            if (_explosionCoroutine != null)
            {
                StopCoroutine(_explosionCoroutine);
            }
            _explosionCoroutine = StartCoroutine(ExplodeAfterDelay());
        }

        private IEnumerator ExplodeAfterDelay()
        {
            // Wait for explosion delay
            yield return new WaitForSeconds(_explosionDelay);

            if (!_hasExploded)
            {
                Explode();
            }
        }

        private void Explode()
        {
            _hasExploded = true;

            var currentPos = GetCurrentCellPosition();

            // Use SpecialBlockController to get destroyable blocks
            var blocksToDestroy = SpecialBlockController.Instance.GetDestroyableBlocksInRadius(
                currentPos, _explosionRadius, this);

            if (_debugMode)
                Debug.Log($"[BombBlock] Exploding at {currentPos}, destroying {blocksToDestroy.Count} blocks");

            // Capture block positions for VFX synchronization
            var blockPositions = new List<Vector3>();
            foreach (var block in blocksToDestroy)
            {
                if (block != null && block.transform != null)
                {
                    blockPositions.Add(block.transform.position);
                }
            }

            // Broadcast bomb destruction marked event for VFX synchronization
            var destructionMarkedEvent = new BombDestructionMarkedEvent
            {
                BlockPositions = blockPositions,
                ExplosionCenter = transform.position,
                ExplosionRadius = _explosionRadius,
                ExpectedDestructionDelay = 0f // Immediate destruction in current implementation
            };
            EventManager.Broadcast(destructionMarkedEvent);

            // Broadcast explosion event for immediate VFX (main explosion and screen shake)
            var explosionEvent = new BombExplosionEvent
            {
                ExplosionCenter = transform.position,
                DestroyedBlocks = blocksToDestroy,
                ExplosionRadius = _explosionRadius,
                SourceBomb = this
            };
            EventManager.Broadcast(explosionEvent);

            // Broadcast block destruction request
            if (blocksToDestroy.Count > 0)
            {
                var destructionEvent = new BlockDestructionRequestEvent
                {
                    BlocksToDestroy = blocksToDestroy,
                    ExplosionCenter = transform.position,
                    PlayDestructionEffects = true
                };

                EventManager.Broadcast(destructionEvent);
            }

            // Self-destruct after a brief delay
            StartCoroutine(SelfDestruct());
        }

        private List<Cell> GetAffectedCells()
        {
            if (SpecialBlockController.Instance == null)
            {
                Debug.LogError("[BombBlock] SpecialBlockController instance not found!");
                return new List<Cell>();
            }

            var currentPos = GetCurrentCellPosition();
            var affectedCells = SpecialBlockController.Instance.GetCellsInRadius(currentPos, _explosionRadius, true);

            if (_debugMode)
                Debug.Log($"[BombBlock] Found {affectedCells.Count} affected cells in radius {_explosionRadius}");

            return affectedCells;
        }

        private Vector2Int GetCurrentCellPosition()
        {
            if (SpecialBlockController.Instance == null)
            {
                Debug.LogError("[BombBlock] SpecialBlockController instance not found!");
                return Vector2Int.zero;
            }

            return SpecialBlockController.Instance.GetCellCoordinates(transform.position);
        }

        private IEnumerator SelfDestruct()
        {
            yield return new WaitForSeconds(0.2f);

            if (_debugMode)
                Debug.Log("[BombBlock] Self-destructing");

            if (_destroysSelf)
            {
                // Use SpecialBlockController to destroy this bomb
                var currentPos = GetCurrentCellPosition();
                if (SpecialBlockController.Instance != null)
                {
                    SpecialBlockController.Instance.DestroyBlockAtPosition(currentPos);
                }
                else
                {
                    // Fallback if controller is not available
                    Remove();
                }
            }
        }

        private void OnDestroy()
        {
            if (_explosionCoroutine != null)
            {
                StopCoroutine(_explosionCoroutine);
                _explosionCoroutine = null;
            }
        }

        // Public method for testing/debugging
        public void ForceExplode()
        {
            if (!_hasExploded)
            {
                if (_explosionCoroutine != null)
                {
                    StopCoroutine(_explosionCoroutine);
                }
                Explode();
            }
        }
    }
}