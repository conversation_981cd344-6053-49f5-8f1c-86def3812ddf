using UnityEngine;
using DG.Tweening;

namespace _Tasks.NewFeature.Settings
{
    /// <summary>
    /// ScriptableObject containing all settings for victory and defeat animation sequences
    /// Allows easy tweaking of animation parameters without code changes
    /// </summary>
    [CreateAssetMenu(menuName = "MergeSolitaire/Settings/Result Animations", fileName = "ResultAnimationSettings")]
    public class ResultAnimationSettings : ScriptableObject
    {
        [Header("General Settings")]
        [SerializeField] private float _totalAnimationDuration = 5f;
        [SerializeField] private bool _allowUserSkip = true;
        [SerializeField] private float _delayBeforeSceneTransition = 1f;

        [Header("Victory Settings")]
        [SerializeField] private VictoryAnimationConfig _victoryConfig;

        [Header("Defeat Settings")]
        [SerializeField] private DefeatAnimationConfig _defeatConfig;

        [Header("Score Display Settings")]
        [SerializeField] private ScoreDisplayConfig _scoreDisplayConfig;

        [Header("UI Animation Settings")]
        [SerializeField] private UIAnimationConfig _uiAnimationConfig;

        // Public properties
        public float TotalAnimationDuration => _totalAnimationDuration;
        public bool AllowUserSkip => _allowUserSkip;
        public float DelayBeforeSceneTransition => _delayBeforeSceneTransition;
        public VictoryAnimationConfig VictoryConfig => _victoryConfig;
        public DefeatAnimationConfig DefeatConfig => _defeatConfig;
        public ScoreDisplayConfig ScoreDisplayConfig => _scoreDisplayConfig;
        public UIAnimationConfig UIAnimationConfig => _uiAnimationConfig;
    }

    [System.Serializable]
    public class VictoryAnimationConfig
    {
        [Header("Announcement")]
        public string victoryText = "VICTORY!";
        public Color victoryTextColor = Color.yellow;
        public float announcementDelay = 0.5f;
        public float announcementDuration = 2f;
        public Ease announcementEase = Ease.OutBounce;
        public Vector3 announcementScale = Vector3.one * 1.5f;

        [Header("Particles")]
        public bool enableConfetti = true;
        public bool enableSparkles = true;
        public bool enableGoldenBurst = true;
        public float particleDelay = 1f;
        public float particleDuration = 3f;
        public int confettiCount = 50;
        public int sparkleCount = 30;

        [Header("Screen Effects")]
        public bool enableScreenFlash = true;
        public Color flashColor = new Color(1f, 1f, 0.8f, 0.3f);
        public float flashDuration = 0.2f;
        public bool enableColorGrading = true;
        public float colorGradingIntensity = 0.3f;

        [Header("Audio")]
        public string victorySfxName = "Victory";
        public string celebrationSfxName = "Celebration";
        public float audioDelay = 0.2f;
    }

    [System.Serializable]
    public class DefeatAnimationConfig
    {
        [Header("Announcement")]
        public string defeatText = "GAME OVER";
        public Color defeatTextColor = new Color(0.8f, 0.3f, 0.3f);
        public float announcementDelay = 0.8f;
        public float announcementDuration = 1.5f;
        public Ease announcementEase = Ease.OutQuart;
        public Vector3 announcementScale = Vector3.one * 1.2f;

        [Header("Particles")]
        public bool enableSmoke = true;
        public bool enableDust = true;
        public bool enableFadingEffects = true;
        public float particleDelay = 0.5f;
        public float particleDuration = 2f;
        public int smokeCount = 20;
        public int dustCount = 15;

        [Header("Screen Effects")]
        public bool enableScreenDarken = true;
        public Color darkenColor = new Color(0.2f, 0.2f, 0.2f, 0.4f);
        public float darkenDuration = 1f;
        public bool enableDesaturation = true;
        public float desaturationIntensity = 0.5f;

        [Header("Audio")]
        public string defeatSfxName = "Lost";
        public string disappointmentSfxName = "Disappointment";
        public float audioDelay = 0.3f;

        [Header("Encouragement")]
        public bool showEncouragement = true;
        public string[] encouragementMessages = {
            "Try again!",
            "You can do better!",
            "Don't give up!",
            "Practice makes perfect!"
        };
        public float encouragementDelay = 2f;
        public Color encouragementColor = new Color(0.7f, 0.7f, 0.9f);
        public float encouragementFadeInDuration = 0.6f;
        public Ease encouragementFadeInEase = Ease.OutBack;

        [Header("Performance-Based Encouragement")]
        [Tooltip("Score threshold for 'close to high score' messages (0.8 = 80% of high score)")]
        public float closeToHighScoreThreshold = 0.8f;
        [Tooltip("Score threshold for 'decent score' messages (0.5 = 50% of high score)")]
        public float decentScoreThreshold = 0.5f;

        [Space(5)]
        public string[] firstTimePlayerMessages = {
            "Every expert was once a beginner!",
            "The first step is always the hardest!",
            "Practice makes perfect!"
        };

        public string[] closeToHighScoreMessages = {
            "So close! You can beat that high score!",
            "Amazing progress! Try again!",
            "You're getting better every time!"
        };

        public string[] decentScoreMessages = {
            "Good effort! Keep improving!",
            "You're on the right track!",
            "Nice try! Go for the high score!"
        };

        public string[] lowScoreMessages = {
            "Don't give up! Every game teaches you something!",
            "Keep practicing! You'll get there!",
            "Try a different strategy next time!"
        };
    }

    [System.Serializable]
    public class ScoreDisplayConfig
    {
        [Header("Score Animation")]
        public float scoreCountDuration = 2f;
        public Ease scoreCountEase = Ease.OutQuart;
        public float scoreDelay = 0.3f;
        public bool enableScoreParticles = true;

        [Header("High Score")]
        public Color newHighScoreColor = Color.yellow;
        public float highScoreScaleMultiplier = 1.3f;
        public float highScorePulseDuration = 0.5f;
        public bool enableHighScoreParticles = true;
        public float highScoreFadeInDuration = 0.8f;
        public float highScoreAppearDelay = 1f;
        public Ease highScoreFadeInEase = Ease.OutQuart;

        [Header("Milestone Effects")]
        public bool enableMilestoneEffects = true;
        public Color milestoneColor = new Color(0.9f, 0.6f, 1f);
        public float milestoneEffectDuration = 1f;
        public Vector3 milestoneScale = Vector3.one * 1.2f;
    }

    [System.Serializable]
    public class UIAnimationConfig
    {
        [Header("Panel Animations")]
        public float panelFadeInDuration = 0.8f;
        public Ease panelFadeInEase = Ease.OutQuart;
        public float elementStaggerDelay = 0.1f;

        [Header("Button Animations")]
        public float buttonAppearDelay = 3f;
        public float buttonScaleDuration = 0.5f;
        public Ease buttonScaleEase = Ease.OutBack;
        public Vector3 buttonHoverScale = Vector3.one * 1.1f;
        public float buttonHoverDuration = 0.2f;

        [Header("Text Animations")]
        public float textTypewriterSpeed = 50f; // Characters per second
        public bool enableTextGlow = true;
        public Color textGlowColor = Color.white;
        public float textGlowIntensity = 0.5f;

        [Header("Background")]
        public bool enableBackgroundAnimation = true;
        public float backgroundAnimationSpeed = 0.5f;
        public Color victoryBackgroundTint = new Color(1f, 0.95f, 0.8f, 0.1f);
        public Color defeatBackgroundTint = new Color(0.8f, 0.8f, 0.9f, 0.1f);
    }

    /// <summary>
    /// Utility methods for encouragement message selection
    /// </summary>
    public static class EncouragementMessageUtility
    {
        /// <summary>
        /// Selects an appropriate encouragement message based on player performance
        /// </summary>
        /// <param name="config">Defeat animation configuration containing message arrays and thresholds</param>
        /// <param name="currentScore">Player's current score</param>
        /// <param name="highScore">Player's high score</param>
        /// <returns>Selected encouragement message</returns>
        public static string GetEncouragementMessage(DefeatAnimationConfig config, int currentScore, int highScore)
        {
            if (config == null)
                return "Try again!";

            // Determine performance category and select appropriate messages
            string[] performanceBasedMessages = GetPerformanceBasedMessages(config, currentScore, highScore);

            // Combine with original messages for variety
            var allMessages = CombineMessageArrays(config.encouragementMessages, performanceBasedMessages);

            return allMessages[UnityEngine.Random.Range(0, allMessages.Length)];
        }

        private static string[] GetPerformanceBasedMessages(DefeatAnimationConfig config, int currentScore, int highScore)
        {
            // First time player (score is 0)
            if (currentScore == 0)
            {
                return config.firstTimePlayerMessages;
            }

            // Close to high score
            if (currentScore >= highScore * config.closeToHighScoreThreshold)
            {
                return config.closeToHighScoreMessages;
            }

            // Decent score
            if (currentScore >= highScore * config.decentScoreThreshold)
            {
                return config.decentScoreMessages;
            }

            // Low score
            return config.lowScoreMessages;
        }

        private static string[] CombineMessageArrays(string[] originalMessages, string[] performanceMessages)
        {
            var allMessages = new string[originalMessages.Length + performanceMessages.Length];
            originalMessages.CopyTo(allMessages, 0);
            performanceMessages.CopyTo(allMessages, originalMessages.Length);
            return allMessages;
        }
    }
}
