%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e66422bf95cbfcf488461bf232136f17, type: 3}
  m_Name: ResultAnimationSettings
  m_EditorClassIdentifier: 
  _totalAnimationDuration: 5
  _allowUserSkip: 1
  _delayBeforeSceneTransition: 1
  _victoryConfig:
    victoryText: VICTORY!
    victoryTextColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    announcementDelay: 0.5
    announcementDuration: 2
    announcementEase: 30
    announcementScale: {x: 1.5, y: 1.5, z: 1.5}
    enableConfetti: 1
    enableSparkles: 1
    enableGoldenBurst: 1
    particleDelay: 1
    particleDuration: 3
    confettiCount: 50
    sparkleCount: 30
    enableScreenFlash: 1
    flashColor: {r: 1, g: 1, b: 0.740566, a: 0.4392157}
    flashDuration: 0.2
    enableColorGrading: 1
    colorGradingIntensity: 0.3
    victorySfxName: Victory
    celebrationSfxName: Celebration
    audioDelay: 0.2
  _defeatConfig:
    defeatText: GAME OVER
    defeatTextColor: {r: 0.8, g: 0.3, b: 0.3, a: 1}
    announcementDelay: 0.8
    announcementDuration: 1.5
    announcementEase: 12
    announcementScale: {x: 1.2, y: 1.2, z: 1.2}
    enableSmoke: 1
    enableDust: 1
    enableFadingEffects: 1
    particleDelay: 0.5
    particleDuration: 2
    smokeCount: 20
    dustCount: 15
    enableScreenDarken: 1
    darkenColor: {r: 0.2, g: 0.2, b: 0.2, a: 0.4}
    darkenDuration: 1
    enableDesaturation: 1
    desaturationIntensity: 0.5
    defeatSfxName: Lost
    disappointmentSfxName: Disappointment
    audioDelay: 0.3
    showEncouragement: 1
    encouragementMessages:
    - Try again!
    - You can do better!
    - Don't give up!
    - Practice makes perfect!
    encouragementDelay: 2
    encouragementColor: {r: 0.7, g: 0.7, b: 0.9, a: 1}
    encouragementFadeInDuration: 0.6
    encouragementFadeInEase: 27
  _scoreDisplayConfig:
    scoreCountDuration: 2
    scoreCountEase: 12
    scoreDelay: 0.3
    enableScoreParticles: 1
    newHighScoreColor: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    highScoreScaleMultiplier: 1.3
    highScorePulseDuration: 0.5
    enableHighScoreParticles: 1
    highScoreFadeInDuration: 0.8
    highScoreAppearDelay: 1
    highScoreFadeInEase: 12
    enableMilestoneEffects: 1
    milestoneColor: {r: 0.9, g: 0.6, b: 1, a: 1}
    milestoneEffectDuration: 1
    milestoneScale: {x: 1.2, y: 1.2, z: 1.2}
  _uiAnimationConfig:
    panelFadeInDuration: 0.8
    panelFadeInEase: 12
    elementStaggerDelay: 0.1
    buttonAppearDelay: 3
    buttonScaleDuration: 0.5
    buttonScaleEase: 27
    buttonHoverScale: {x: 1.1, y: 1.1, z: 1.1}
    buttonHoverDuration: 0.2
    textTypewriterSpeed: 50
    enableTextGlow: 1
    textGlowColor: {r: 1, g: 1, b: 1, a: 1}
    textGlowIntensity: 0.5
    enableBackgroundAnimation: 1
    backgroundAnimationSpeed: 0.5
    victoryBackgroundTint: {r: 1, g: 0.95, b: 0.8, a: 0.1}
    defeatBackgroundTint: {r: 0.8, g: 0.8, b: 0.9, a: 0.1}
