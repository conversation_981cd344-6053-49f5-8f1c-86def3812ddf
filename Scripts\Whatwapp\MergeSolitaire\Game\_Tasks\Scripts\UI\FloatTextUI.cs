using TMPro;
using UnityEngine;

public class FloatTextUI : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI _floatingText;

    private RectTransform _rectTransform;

    /// <summary>
    /// Cached RectTransform component for efficient access
    /// </summary>
    public RectTransform RectTransform
    {
        get
        {
            if (_rectTransform == null)
                _rectTransform = GetComponent<RectTransform>();
            return _rectTransform;
        }
    }

    /// <summary>
    /// Text component for direct access without GetComponent calls
    /// </summary>
    public TextMeshProUGUI TextComponent => _floatingText;

    private void Awake()
    {
        // Cache RectTransform on awake for performance
        _rectTransform = GetComponent<RectTransform>();
    }

    /// <summary>
    /// Set text properties without affecting position (for UI space positioning)
    /// </summary>
    public void SetTextProperties(Color color, string text)
    {
        if (_floatingText != null)
        {
            _floatingText.color = color;
            _floatingText.text = text;
        }
    }

    /// <summary>
    /// Set local UI position directly
    /// </summary>
    public void SetLocalPosition(Vector2 localPosition)
    {
        RectTransform.localPosition = localPosition;
    }

    /// <summary>
    /// Legacy method for backward compatibility (deprecated - use SetTextProperties + SetLocalPosition)
    /// </summary>
    [System.Obsolete("Use SetTextProperties and SetLocalPosition instead for better UI coordinate handling")]
    public void Set(Vector3 position, Color color, string score)
    {
        transform.position = position;
        SetTextProperties(color, score);
    }
}
