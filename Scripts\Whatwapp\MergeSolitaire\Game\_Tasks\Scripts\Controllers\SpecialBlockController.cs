using System.Collections.Generic;
using _Tasks.Events;
using UnityEngine;
using Whatwapp.MergeSolitaire.Game;

namespace _Tasks.NewFeature.Controllers
{
    /// <summary>
    /// Centralized controller for managing special block operations and board interactions.
    /// Provides services to special blocks without requiring them to directly reference the board.
    /// </summary>
    public class SpecialBlockController : MonoBehaviour
    {
        [Header("References")]
        [SerializeField] private Board _board;
        
        [Header("Debug")]
        [SerializeField] private bool _debugMode = false;

        private static SpecialBlockController _instance;
        public static SpecialBlockController Instance => _instance;

        private void Awake()
        {
            // Singleton pattern for easy access
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            // Auto-find board if not assigned
            if (_board == null)
            {
                _board = FindObjectOfType<Board>();
            }

            // Subscribe to events
            EventManager.Subscribe<BombPlacedEvent>(OnBombPlaced);
            EventManager.Subscribe<BombMovementStoppedEvent>(OnBombMovementStopped);
            EventManager.Subscribe<BlockDestructionRequestEvent>(OnBlockDestructionRequested);
        }

        private void OnValidate()
        {
            // Validate board reference in editor
            if (_board == null)
            {
                _board = FindObjectOfType<Board>();
                if (_board == null && Application.isPlaying)
                {
                    Debug.LogWarning("[SpecialBlockController] Board reference is missing! Please assign a Board or ensure one exists in the scene.");
                }
            }

            // Validate singleton instance
            if (Application.isPlaying && _instance != null && _instance != this)
            {
                Debug.LogWarning("[SpecialBlockController] Multiple SpecialBlockController instances detected! Only one should exist.");
            }

            // Validate debug settings
            if (_debugMode && !Application.isPlaying)
            {
                Debug.Log("[SpecialBlockController] Debug mode is enabled. This will generate console logs during gameplay.");
            }
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            EventManager.Unsubscribe<BombPlacedEvent>(OnBombPlaced);
            EventManager.Unsubscribe<BombMovementStoppedEvent>(OnBombMovementStopped);
            EventManager.Unsubscribe<BlockDestructionRequestEvent>(OnBlockDestructionRequested);

            if (_instance == this)
            {
                _instance = null;
            }
        }

        #region Public API for Special Blocks

        /// <summary>
        /// Get the cell coordinates for a given world position
        /// </summary>
        public Vector2Int GetCellCoordinates(Vector3 worldPosition)
        {
            if (_board == null)
            {
                Debug.LogError("[SpecialBlockController] Board reference is null!");
                return Vector2Int.zero;
            }

            return _board.GetCellCoordinates(worldPosition);
        }

        /// <summary>
        /// Get a cell at specific coordinates
        /// </summary>
        public Cell GetCell(Vector2Int coordinates)
        {
            if (_board == null)
            {
                Debug.LogError("[SpecialBlockController] Board reference is null!");
                return null;
            }

            return _board.GetCell(coordinates);
        }

        /// <summary>
        /// Get a cell at specific coordinates
        /// </summary>
        public Cell GetCell(int x, int y)
        {
            if (_board == null)
            {
                Debug.LogError("[SpecialBlockController] Board reference is null!");
                return null;
            }

            return _board.GetCell(x, y);
        }

        /// <summary>
        /// Get all cells within a radius from a center position
        /// </summary>
        public List<Cell> GetCellsInRadius(Vector2Int centerPos, int radius, bool excludeCenter = true)
        {
            var affectedCells = new List<Cell>();

            if (_board == null)
            {
                Debug.LogError("[SpecialBlockController] Board reference is null!");
                return affectedCells;
            }

            for (int x = -radius; x <= radius; x++)
            {
                for (int y = -radius; y <= radius; y++)
                {
                    // Skip center if requested
                    if (excludeCenter && x == 0 && y == 0) continue;

                    var targetPos = centerPos + new Vector2Int(x, y);
                    var cell = _board.GetCell(targetPos.x, targetPos.y);

                    if (cell != null)
                    {
                        affectedCells.Add(cell);
                    }
                }
            }

            return affectedCells;
        }

        /// <summary>
        /// Get all blocks within a radius that can be destroyed (excludes the source block)
        /// </summary>
        public List<Block> GetDestroyableBlocksInRadius(Vector2Int centerPos, int radius, Block excludeBlock = null)
        {
            var blocks = new List<Block>();
            var cells = GetCellsInRadius(centerPos, radius, true);

            foreach (var cell in cells)
            {
                if (!cell.IsEmpty && cell.Block != excludeBlock)
                {
                    blocks.Add(cell.Block);
                }
            }

            return blocks;
        }

        /// <summary>
        /// Clear a block from its cell and destroy it
        /// </summary>
        public void DestroyBlockAtPosition(Vector2Int position)
        {
            var cell = GetCell(position);
            if (cell != null && !cell.IsEmpty)
            {
                var block = cell.Block;

                // Safely kill animations before destroying
                var animationController = FindObjectOfType<_Tasks.NewFeature.Animation.AnimationController>();
                if (animationController != null)
                {
                    animationController.SafelyKillBlockAnimations(block);
                }

                cell.Block = null;
                block.Remove();

                if (_debugMode)
                    Debug.Log($"[SpecialBlockController] Destroyed block at {position}");
            }
        }

        /// <summary>
        /// Clear multiple blocks from their cells and destroy them
        /// </summary>
        public void DestroyBlocks(List<Block> blocks)
        {
            // Get AnimationController for safe cleanup
            var animationController = FindObjectOfType<_Tasks.NewFeature.Animation.AnimationController>();

            foreach (var block in blocks)
            {
                if (block == null) continue;

                // Safely kill animations before destroying
                if (animationController != null)
                {
                    animationController.SafelyKillBlockAnimations(block);
                }

                // Find the cell containing this block and clear it
                var blockPosition = GetCellCoordinates(block.transform.position);
                var cell = GetCell(blockPosition);

                if (cell != null && cell.Block == block)
                {
                    cell.Block = null;
                }

                block.Remove();
            }

            if (_debugMode)
                Debug.Log($"[SpecialBlockController] Destroyed {blocks.Count} blocks");
        }

        #endregion

        #region Event Handlers

        private void OnBombPlaced(BombPlacedEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[SpecialBlockController] Bomb placed at {eventData.Position}");
        }

        private void OnBombMovementStopped(BombMovementStoppedEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[SpecialBlockController] Bomb movement stopped at {eventData.FinalPosition}, affecting {eventData.AffectedCells.Count} cells");
        }

        private void OnBlockDestructionRequested(BlockDestructionRequestEvent eventData)
        {
            // Handle block destruction requests
            int destroyedCount = eventData.BlocksToDestroy.Count;
            DestroyBlocks(eventData.BlocksToDestroy);

            if (_debugMode)
                Debug.Log($"[SpecialBlockController] Processed destruction request for {destroyedCount} blocks");

            // Broadcast blocks destroyed event to trigger game state updates
            if (destroyedCount > 0)
            {
                var blocksDestroyedEvent = new BlocksDestroyedEvent
                {
                    DestroyedBlockCount = destroyedCount,
                    DestructionCenter = eventData.ExplosionCenter,
                    RequiresMovement = true, // Blocks need to fall after destruction
                    RequiresMergeCheck = true // May need to check for new merges after movement
                };

                EventManager.Broadcast(blocksDestroyedEvent);

                if (_debugMode)
                    Debug.Log($"[SpecialBlockController] Broadcasted BlocksDestroyedEvent for {destroyedCount} blocks");
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Check if the board is available
        /// </summary>
        public bool IsBoardAvailable()
        {
            return _board != null;
        }

        /// <summary>
        /// Get the board reference for special blocks that need direct access
        /// </summary>
        public Board GetBoard()
        {
            return _board;
        }

        /// <summary>
        /// Get board dimensions
        /// </summary>
        public Vector2Int GetBoardSize()
        {
            if (_board == null) return Vector2Int.zero;
            return new Vector2Int(_board.Width, _board.Height);
        }

        /// <summary>
        /// Check if coordinates are within board bounds
        /// </summary>
        public bool IsValidPosition(Vector2Int position)
        {
            if (_board == null) return false;
            return position.x >= 0 && position.x < _board.Width && 
                   position.y >= 0 && position.y < _board.Height;
        }

        #endregion
    }
}
