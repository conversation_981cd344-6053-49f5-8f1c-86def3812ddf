using System.Collections;
using UnityEngine;
using DG.Tweening;
using _Tasks.Events;
using _Tasks.NewFeature.Settings;
using _Tasks.NewFeature.VFX;
using Whatwapp.Core.Audio;

namespace _Tasks.NewFeature.Animation
{
    /// <summary>
    /// Simplified controller for victory and defeat animation sequences
    /// Orchestrates UI animations, particle effects, and screen transitions
    /// Uses event-driven pattern for modular and decoupled design
    /// </summary>
    public class ResultAnimationController : MonoBehaviour
    {
        [Header("Settings")]
        [SerializeField] private ResultAnimationSettings _animationSettings;

        [Header("UI References")]
        [SerializeField] private CanvasGroup _resultPanel;
        [SerializeField] private TMPro.TMP_Text _resultText;
        [SerializeField] private TMPro.TMP_Text _scoreText;
        [SerializeField] private TMPro.TMP_Text _highScoreLable;
        [SerializeField] private TMPro.TMP_Text _highScoreText;
        [SerializeField] private CanvasGroup _highScoreGroup;
        [SerializeField] private TMPro.TMP_Text _encouragementText;
        [SerializeField] private CanvasGroup _encouragementGroup;
        [SerializeField] private UnityEngine.UI.Button _newGameButton;
        [SerializeField] private UnityEngine.UI.Image _backgroundOverlay;

        [Header("Particle References")]
        [SerializeField] private Transform _particleParent;
        [SerializeField] private GameObject _confettiPrefab;
        [SerializeField] private GameObject _sparklesPrefab;
        [SerializeField] private GameObject _goldenBurstPrefab;
        [SerializeField] private GameObject _smokePrefab;
        [SerializeField] private GameObject _dustPrefab;

        [Header("Debug")]
        [SerializeField] private bool _debugMode = false;

        public static ResultAnimationController Instance { get; private set; }

        private SFXManager _sfxManager;
        private Sequence _animationSequence;
        private bool _animationInProgress = false;
        private bool _userSkipped = false;
        private bool _isVictoryAnimation = false;

        #region Unity Lifecycle

        private void Awake()
        {
            // Simplified singleton pattern
            if (Instance == null)
            {
                Instance = this;
                InitializeController();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void OnDestroy()
        {
            // Clean up
            UnsubscribeFromEvents();
            _animationSequence?.Kill();

            if (Instance == this)
                Instance = null;
        }

        private void Update()
        {
            // Allow user to skip animation with any key
            if (_animationInProgress && _animationSettings != null && _animationSettings.AllowUserSkip && Input.anyKeyDown)
            {
                SkipAnimation();
            }
        }

        #endregion

        #region Initialization

        private void InitializeController()
        {
            // Initialize references
            _sfxManager = SFXManager.Instance;

            // Subscribe to events
            SubscribeToEvents();

            // Setup initial state
            SetupInitialState();
        }

        private void SubscribeToEvents()
        {
            EventManager.Subscribe<GameVictoryEvent>(OnGameVictory);
            EventManager.Subscribe<GameDefeatEvent>(OnGameDefeat);
            EventManager.Subscribe<ResultAnimationStartEvent>(OnResultAnimationStart);
        }

        private void UnsubscribeFromEvents()
        {
            EventManager.Unsubscribe<GameVictoryEvent>(OnGameVictory);
            EventManager.Unsubscribe<GameDefeatEvent>(OnGameDefeat);
            EventManager.Unsubscribe<ResultAnimationStartEvent>(OnResultAnimationStart);
        }

        #endregion

        #region Event Handlers

        private void OnGameVictory(GameVictoryEvent victoryEvent)
        {
            if (_debugMode)
                Debug.Log($"[ResultAnimationController] Victory event received. Score: {victoryEvent.FinalScore}");

            if (!_animationInProgress)
                StartCoroutine(PlayVictorySequence(victoryEvent));
        }

        private void OnGameDefeat(GameDefeatEvent defeatEvent)
        {
            if (_debugMode)
                Debug.Log($"[ResultAnimationController] Defeat event received. Score: {defeatEvent.FinalScore}");

            if (!_animationInProgress)
                StartCoroutine(PlayDefeatSequence(defeatEvent));
        }

        private void OnResultAnimationStart(ResultAnimationStartEvent startEvent)
        {
            if (_debugMode)
                Debug.Log($"[ResultAnimationController] Animation start event received. Victory: {startEvent.IsVictory}");

            if (startEvent.SkipToEnd)
                SkipAnimation();
        }

        #endregion

        #region Animation Sequences

        private IEnumerator PlayVictorySequence(GameVictoryEvent victoryEvent)
        {
            if (_animationSettings == null) yield break;

            _animationInProgress = true;
            _userSkipped = false;
            _isVictoryAnimation = true;

            var config = _animationSettings.VictoryConfig;

            // Create a single sequence for all animations
            _animationSequence = DOTween.Sequence();

            // Add all animation steps to the sequence
            AddShowResultPanelToSequence();
            AddAnnouncementToSequence(config.victoryText, config.victoryTextColor, config, true);
            AddScoreAnimationToSequence(victoryEvent.FinalScore, victoryEvent.IsNewHighScore);
            AddParticlesToSequence(true, config, Vector3.zero);
            AddShowNewGameButtonToSequence();

            // Set completion callback
            _animationSequence.OnComplete(() => CompleteAnimation(true));

            // Wait for sequence to complete
            yield return _animationSequence.WaitForCompletion();
        }

        private IEnumerator PlayDefeatSequence(GameDefeatEvent defeatEvent)
        {
            if (_animationSettings == null) yield break;

            _animationInProgress = true;
            _userSkipped = false;
            _isVictoryAnimation = false;

            var config = _animationSettings.DefeatConfig;

            // Create a single sequence for all animations
            _animationSequence = DOTween.Sequence();

            // Add all animation steps to the sequence
            AddShowResultPanelToSequence();
            AddAnnouncementToSequence(config.defeatText, config.defeatTextColor, config, false);
            AddScoreAnimationToSequence(defeatEvent.FinalScore, false);
            AddParticlesToSequence(false, config, Vector3.zero);

            if (config.showEncouragement)
                AddShowEncouragementToSequence(config);

            AddShowNewGameButtonToSequence();

            // Set completion callback
            _animationSequence.OnComplete(() => CompleteAnimation(false));

            // Wait for sequence to complete
            yield return _animationSequence.WaitForCompletion();
        }

        #endregion

        #region Helper Methods

        private void SetupInitialState()
        {
            if (_resultPanel != null)
            {
                _resultPanel.alpha = 0f;
                _resultPanel.blocksRaycasts = false;
                _resultPanel.interactable = false;
            }

            // Reset all UI elements to initial state
            ResetUIElement(_resultText);
            ResetUIElement(_scoreText);
            ResetUIElement(_highScoreText);
            ResetUIElement(_newGameButton);

            // Setup canvas groups for high score and encouragement
            SetupCanvasGroup(_highScoreGroup);
            SetupCanvasGroup(_encouragementGroup);

            if (_backgroundOverlay != null)
                _backgroundOverlay.color = Color.clear;
        }

        private void SetupCanvasGroup(CanvasGroup canvasGroup)
        {
            if (canvasGroup != null)
            {
                canvasGroup.alpha = 0f;
                canvasGroup.blocksRaycasts = false;
                canvasGroup.interactable = false;
            }
        }

        private void ResetUIElement(Component element)
        {
            if (element != null)
                element.transform.localScale = Vector3.zero;
        }

        private void SkipAnimation()
        {
            if (!_animationInProgress) return;

            _userSkipped = true;
            _animationSequence?.Kill();

            // Jump to final state for all UI elements
            if (_resultPanel != null)
            {
                _resultPanel.alpha = 1f;
                _resultPanel.blocksRaycasts = true;
                _resultPanel.interactable = true;
            }

            if (_newGameButton != null)
                _newGameButton.transform.localScale = Vector3.one;

            // Only show high score group for victory animations
            if (_isVictoryAnimation && _highScoreGroup != null)
            {
                _highScoreGroup.alpha = 1f;
                _highScoreGroup.blocksRaycasts = true;
                _highScoreGroup.interactable = true;
            }

            // Only show encouragement group for defeat animations
            if (!_isVictoryAnimation && _encouragementGroup != null)
            {
                _encouragementGroup.alpha = 1f;
                _encouragementGroup.blocksRaycasts = true;
                _encouragementGroup.interactable = true;
            }

            // Set text elements to final scale based on animation type
            if (_resultText != null) _resultText.transform.localScale = Vector3.one;
            if (_scoreText != null) _scoreText.transform.localScale = Vector3.one;

            // Only scale high score text for victory animations
            if (_isVictoryAnimation && _highScoreText != null)
                _highScoreText.transform.localScale = Vector3.one;

            // Only scale encouragement text for defeat animations
            if (!_isVictoryAnimation && _encouragementText != null)
                _encouragementText.transform.localScale = Vector3.one;

            CompleteAnimation(_userSkipped);
        }

        private void CompleteAnimation(bool isVictory)
        {
            _animationInProgress = false;
            _isVictoryAnimation = false; // Reset the flag

            if (_animationSettings != null)
            {
                EventManager.Broadcast(new ResultAnimationCompleteEvent
                {
                    IsVictory = isVictory,
                    TotalAnimationTime = _animationSettings.TotalAnimationDuration,
                    UserSkipped = _userSkipped
                });
            }

            if (_debugMode)
                Debug.Log($"[ResultAnimationController] Animation completed. Victory: {isVictory}, Skipped: {_userSkipped}");
        }

        #endregion

        #region Animation Implementation Methods

        private void AddShowResultPanelToSequence()
        {
            if (_resultPanel == null || _animationSettings == null) return;

            var config = _animationSettings.UIAnimationConfig;
            _resultPanel.blocksRaycasts = true;
            _resultPanel.interactable = true;

            _animationSequence.Append(_resultPanel.DOFade(1f, config.panelFadeInDuration)
                .SetEase(config.panelFadeInEase));
        }

        private void AddAnnouncementToSequence(string text, Color textColor, object config, bool isVictory)
        {
            if (_resultText == null) return;

            // Setup text
            _resultText.text = text;
            _resultText.color = textColor;

            if (isVictory && config is VictoryAnimationConfig victoryConfig)
            {
                // Add audio delay
                if (!string.IsNullOrEmpty(victoryConfig.victorySfxName))
                {
                    _animationSequence.AppendInterval(victoryConfig.audioDelay);
                    _animationSequence.AppendCallback(() => _sfxManager?.PlayOneShot(victoryConfig.victorySfxName));
                }

                // Add announcement delay and animation
                _animationSequence.AppendInterval(victoryConfig.announcementDelay);
                _animationSequence.Append(_resultText.transform.DOScale(victoryConfig.announcementScale, victoryConfig.announcementDuration)
                    .SetEase(victoryConfig.announcementEase));

                // Screen flash effect
                if (victoryConfig.enableScreenFlash && _backgroundOverlay != null)
                {
                    _animationSequence.AppendCallback(() => _backgroundOverlay.color = victoryConfig.flashColor);
                    _animationSequence.Append(_backgroundOverlay.DOFade(0f, victoryConfig.flashDuration));
                }
            }
            else if (!isVictory && config is DefeatAnimationConfig defeatConfig)
            {
                // Add audio delay
                if (!string.IsNullOrEmpty(defeatConfig.defeatSfxName))
                {
                    _animationSequence.AppendInterval(defeatConfig.audioDelay);
                    _animationSequence.AppendCallback(() => _sfxManager?.PlayOneShot(defeatConfig.defeatSfxName));
                }

                // Add announcement delay and animation
                _animationSequence.AppendInterval(defeatConfig.announcementDelay);
                _animationSequence.Append(_resultText.transform.DOScale(defeatConfig.announcementScale, defeatConfig.announcementDuration)
                    .SetEase(defeatConfig.announcementEase));

                // Screen darken effect
                if (defeatConfig.enableScreenDarken && _backgroundOverlay != null)
                {
                    _animationSequence.Append(_backgroundOverlay.DOColor(defeatConfig.darkenColor, defeatConfig.darkenDuration));
                }
            }
        }

        private void AddScoreAnimationToSequence(int finalScore, bool isNewHighScore)
        {
            if (_scoreText == null || _animationSettings == null) return;

            var config = _animationSettings.ScoreDisplayConfig;

            // Add score delay
            _animationSequence.AppendInterval(config.scoreDelay);

            // Scale in score text
            _animationSequence.Append(_scoreText.transform.DOScale(Vector3.one, 0.5f)
                .SetEase(Ease.OutBack));

            // Get previous high score from PlayerPrefs for display
            int previousHighScore = UnityEngine.PlayerPrefs.GetInt("HighScore", 0);

            // Animate score counting
            int currentScore = 0;
            _animationSequence.Append(DOTween.To(() => currentScore, x => {
                currentScore = x;
                _scoreText.text = $"Score: {currentScore:N0}";
            }, finalScore, config.scoreCountDuration)
            .SetEase(config.scoreCountEase));

            // Show previous high score for context
            if (_highScoreText != null && previousHighScore > 0)
            {
                _animationSequence.AppendCallback(() => {
                    _highScoreLable.text = $"Previous Best";
                    _highScoreText.text = $"{previousHighScore:N0}";
                    _highScoreText.color = Color.gray;
                    _highScoreText.transform.localScale = Vector3.one * 0.8f;
                });
            }

            // High score effects with canvas group
            if (isNewHighScore && _highScoreGroup != null && _highScoreText != null)
            {
                _animationSequence.AppendInterval(config.highScoreAppearDelay);

                // Setup high score text
                _animationSequence.AppendCallback(() => {
                    _highScoreLable.text = "NEW HIGH SCORE!";
                    _highScoreLable.color = config.newHighScoreColor;
                    _highScoreLable.transform.localScale = new Vector3(0,1,1);
                    _highScoreGroup.blocksRaycasts = true;
                    _highScoreGroup.interactable = true;
                });
            }
            
            // Fade in the high score group
            _animationSequence.Append(_highScoreGroup.DOFade(1f, config.highScoreFadeInDuration)
                .SetEase(config.highScoreFadeInEase));

            // Scale animation for the text
            _animationSequence.Append(_highScoreText.transform.DOScale(Vector3.one * config.highScoreScaleMultiplier, config.highScorePulseDuration)
                .SetEase(Ease.OutElastic));

            // Pulse effect
            _animationSequence.Append(_highScoreText.transform.DOScale(Vector3.one, config.highScorePulseDuration)
                .SetEase(Ease.OutElastic));
        }

        private void AddParticlesToSequence(bool isVictory, object config, Vector3 position)
        {
            if (_particleParent == null) return;

            if (isVictory && config is VictoryAnimationConfig victoryConfig)
            {
                _animationSequence.AppendInterval(victoryConfig.particleDelay);

                // Victory particles
                _animationSequence.AppendCallback(() => {
                    CreateParticle(victoryConfig.enableConfetti, _confettiPrefab, position, victoryConfig.particleDuration);
                    CreateParticle(victoryConfig.enableSparkles, _sparklesPrefab, position, victoryConfig.particleDuration);
                });

                if (victoryConfig.enableGoldenBurst && _goldenBurstPrefab != null)
                {
                    _animationSequence.AppendInterval(0.5f);
                    _animationSequence.AppendCallback(() => {
                        CreateParticle(true, _goldenBurstPrefab, position, victoryConfig.particleDuration);
                    });
                }
            }
            else if (!isVictory && config is DefeatAnimationConfig defeatConfig)
            {
                _animationSequence.AppendInterval(defeatConfig.particleDelay);

                // Defeat particles
                _animationSequence.AppendCallback(() => {
                    CreateParticle(defeatConfig.enableSmoke, _smokePrefab, position, defeatConfig.particleDuration);
                    CreateParticle(defeatConfig.enableDust, _dustPrefab, position, defeatConfig.particleDuration);
                });
            }
        }

        private void CreateParticle(bool enabled, GameObject prefab, Vector3 position, float duration)
        {
            if (enabled && prefab != null && _particleParent != null)
            {
                var particle = Instantiate(prefab, position, Quaternion.identity, _particleParent);
                Destroy(particle, duration);
            }
        }

        private void AddShowEncouragementToSequence(DefeatAnimationConfig config)
        {
            if (_encouragementText == null || _encouragementGroup == null || config.encouragementMessages.Length == 0)
                return;

            // Get current score and high score from PlayerPrefs to determine encouragement type
            int currentScore = UnityEngine.PlayerPrefs.GetInt("LastScore", 0);
            int highScore = UnityEngine.PlayerPrefs.GetInt("HighScore", 0);

            _animationSequence.AppendInterval(config.encouragementDelay);

            // Select appropriate encouragement message based on performance
            _animationSequence.AppendCallback(() => {
                string message = EncouragementMessageUtility.GetEncouragementMessage(config, currentScore, highScore);
                _encouragementText.text = message;
                _encouragementText.color = config.encouragementColor;
                _encouragementText.transform.localScale = new Vector3(0,1,1);
                _encouragementGroup.blocksRaycasts = true;
                _encouragementGroup.interactable = true;
            });

            // Fade in the encouragement group
            _animationSequence.Append(_encouragementGroup.DOFade(1f, config.encouragementFadeInDuration)
                .SetEase(config.encouragementFadeInEase));

            // Scale in the text
            _animationSequence.Append(_encouragementText.transform.DOScale(Vector3.one, 0.5f)
                .SetEase(Ease.OutBack));
        }



        private void AddShowNewGameButtonToSequence()
        {
            if (_newGameButton == null || _animationSettings == null) return;

            var config = _animationSettings.UIAnimationConfig;
            _animationSequence.AppendInterval(config.buttonAppearDelay);

            // Scale in button
            _animationSequence.Append(_newGameButton.transform.DOScale(Vector3.one, config.buttonScaleDuration)
                .SetEase(config.buttonScaleEase));

            // Setup hover effects
            _animationSequence.AppendCallback(() => SetupButtonHoverEffects());
        }

        private void SetupButtonHoverEffects()
        {
            if (_newGameButton == null || _animationSettings == null) return;

            var config = _animationSettings.UIAnimationConfig;
            var eventTrigger = _newGameButton.gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>()
                ?? _newGameButton.gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();

            // Clear existing triggers to avoid duplicates
            eventTrigger.triggers.Clear();

            // Hover enter
            var pointerEnter = new UnityEngine.EventSystems.EventTrigger.Entry
            {
                eventID = UnityEngine.EventSystems.EventTriggerType.PointerEnter
            };
            pointerEnter.callback.AddListener((data) => {
                _newGameButton.transform.DOScale(config.buttonHoverScale, config.buttonHoverDuration)
                    .SetEase(Ease.OutQuart);
            });
            eventTrigger.triggers.Add(pointerEnter);

            // Hover exit
            var pointerExit = new UnityEngine.EventSystems.EventTrigger.Entry
            {
                eventID = UnityEngine.EventSystems.EventTriggerType.PointerExit
            };
            pointerExit.callback.AddListener((data) => {
                _newGameButton.transform.DOScale(Vector3.one, config.buttonHoverDuration)
                    .SetEase(Ease.OutQuart);
            });
            eventTrigger.triggers.Add(pointerExit);
        }

        #endregion
    }
}
