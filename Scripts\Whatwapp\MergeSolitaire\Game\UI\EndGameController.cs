using UnityEngine;
using UnityEngine.UI;
using _Tasks.Events;
using _Tasks.NewFeature.Animation;

namespace Whatwapp.MergeSolitaire.Game.UI
{
    public class EndGameController : MonoBehaviour
    {
        [Header("References")]
        [SerializeField] private ScoreBox _scoreBox;
        [SerializeField] private ResultAnimationController _resultAnimationController;

        [Header("Debug")]
        [SerializeField] private bool _debugMode = false;

        private bool _gameWon;
        private int _finalScore;
        private int _highScore;
        private bool _isNewHighScore;

        private void Start()
        {
            // Get game result data
            _gameWon = PlayerPrefs.GetInt(Consts.PREFS_LAST_WON, 0) == 1;
            _finalScore = PlayerPrefs.GetInt(Consts.PREFS_LAST_SCORE, 0);
            _highScore = PlayerPrefs.GetInt(Consts.PREFS_HIGHSCORE, 0);
            _isNewHighScore = _finalScore > _highScore;

            if (_debugMode)
                Debug.Log($"[EndGameController] Game Won: {_gameWon}, Final Score: {_finalScore}, High Score: {_highScore}, New High Score: {_isNewHighScore}");

            // Set initial score display
            _scoreBox.SetScore(_finalScore, false);

            // Subscribe to animation completion event
            EventManager.Subscribe<ResultAnimationCompleteEvent>(OnAnimationComplete);

            // Trigger appropriate animation based on game result
            TriggerResultAnimation();
        }

        private void OnDestroy()
        {
            EventManager.Unsubscribe<ResultAnimationCompleteEvent>(OnAnimationComplete);
        }

        private void TriggerResultAnimation()
        {
            Vector3 scorePosition = _scoreBox != null ? _scoreBox.transform.position : Vector3.zero;

            if (_gameWon)
            {
                // Trigger victory animation
                EventManager.Broadcast(new GameVictoryEvent
                {
                    FinalScore = _finalScore,
                    PreviousHighScore = _highScore,
                    IsNewHighScore = _isNewHighScore,
                    VictoryBonus = Consts.VICTORY_POINTS,
                    CompletionTime = Time.time, // Could be tracked more accurately
                    MilestonesAchieved = _finalScore / 100, // Simple milestone calculation
                    ScorePosition = scorePosition,
                    PlayCelebrationParticles = true,
                    PlayVictorySound = true,
                    VictoryMessage = _isNewHighScore ? "NEW HIGH SCORE!" : "VICTORY!"
                });
            }
            else
            {
                // Trigger defeat animation
                EventManager.Broadcast(new GameDefeatEvent
                {
                    FinalScore = _finalScore,
                    HighScore = _highScore,
                    SurvivalTime = Time.time, // Could be tracked more accurately
                    BlocksPlaced = 0, // Could be tracked if needed
                    LastMilestone = (_finalScore / 100) * 100,
                    ScorePosition = scorePosition,
                    PlayDefeatParticles = true,
                    PlayDefeatSound = true,
                    DefeatReason = "Game Over",
                    ShowEncouragement = true
                });
            }
        }

        private void OnAnimationComplete(ResultAnimationCompleteEvent animationEvent)
        {
            if (_debugMode)
                Debug.Log($"[EndGameController] Animation completed. Victory: {animationEvent.IsVictory}, Skipped: {animationEvent.UserSkipped}");

            // Animation is complete, UI should now be interactive
            // Additional logic can be added here if needed
        }
    }
}