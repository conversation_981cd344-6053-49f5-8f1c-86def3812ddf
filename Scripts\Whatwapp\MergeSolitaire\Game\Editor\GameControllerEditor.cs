using UnityEngine;
using UnityEditor;
using Whatwapp.Core.Audio;
using Whatwapp.Core.Utils;
using Whatwapp.MergeSolitaire.Game;
using Whatwapp.MergeSolitaire.Game.GameStates;
using Whatwapp.Core.Utils.Executables;

namespace Whatwapp.MergeSolitaire.Game.Editor
{
    /// <summary>
    /// Simple Unity Editor for GameController to test win/lose scenarios
    /// Provides buttons to trigger victory/defeat states and load EndGame scene
    /// </summary>
    [CustomEditor(typeof(GameController))]
    public class GameControllerEditor : UnityEditor.Editor
    {
        private GameController _gameController;
        private bool _showTestControls = true;
        private bool _showScoreSettings = true;

        // Test parameters
        private int _testScore = 1500;
        private bool _testNewHighScore = false;

        private void OnEnable()
        {
            _gameController = (GameController)target;
        }

        public override void OnInspectorGUI()
        {
            // Draw default inspector
            DrawDefaultInspector();

            if (!Application.isPlaying)
            {
                EditorGUILayout.HelpBox("Game testing is only available in Play Mode", MessageType.Info);
                return;
            }

            if (_gameController == null)
            {
                EditorGUILayout.HelpBox("GameController reference is null", MessageType.Error);
                return;
            }

            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("Game Testing Controls", EditorStyles.boldLabel);

            // Test parameters section
            DrawTestParameters();

            EditorGUILayout.Space(5);

            // Test controls section
            DrawTestControls();
        }

        private void DrawTestParameters()
        {
            _showScoreSettings = EditorGUILayout.Foldout(_showScoreSettings, "Score Settings", true);
            if (_showScoreSettings)
            {
                EditorGUI.indentLevel++;

                _testScore = EditorGUILayout.IntField("Test Score", _testScore);
                _testNewHighScore = EditorGUILayout.Toggle("Force New High Score", _testNewHighScore);

                EditorGUILayout.Space(3);

                // Current game info
                EditorGUILayout.LabelField("Current Game Info", EditorStyles.boldLabel);
                EditorGUI.BeginDisabledGroup(true);
                EditorGUILayout.IntField("Current Score", _gameController.Score);
                EditorGUILayout.IntField("High Score", PlayerPrefs.GetInt(Consts.PREFS_HIGHSCORE, 0));
                EditorGUI.EndDisabledGroup();

                EditorGUI.indentLevel--;
            }
        }

        private void DrawTestControls()
        {
            _showTestControls = EditorGUILayout.Foldout(_showTestControls, "Game State Controls", true);
            if (_showTestControls)
            {
                EditorGUI.indentLevel++;

                EditorGUILayout.LabelField("Win/Lose Testing", EditorStyles.boldLabel);

                EditorGUILayout.BeginHorizontal();

                if (GUILayout.Button("Test GameOver State"))
                {
                    _gameController._stateMachine.SetState(new GameOverState(_gameController,SFXManager.Instance));
                }
                
                // Win Game button
                if (GUILayout.Button("Win Game", GUILayout.Height(30)))
                {
                    TriggerWinGame();
                }

                // Lose Game button
                if (GUILayout.Button("Lose Game", GUILayout.Height(30)))
                {
                    TriggerLoseGame();
                }

                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(5);

                EditorGUILayout.LabelField("Direct Scene Loading", EditorStyles.boldLabel);

                EditorGUILayout.BeginHorizontal();

                // Load EndGame Scene button
                if (GUILayout.Button("📱 Load EndGame Scene", GUILayout.Height(25)))
                {
                    LoadEndGameScene();
                }

                // Load Main Menu button
                if (GUILayout.Button("🏠 Load Main Menu", GUILayout.Height(25)))
                {
                    LoadMainMenuScene();
                }

                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(5);

                EditorGUILayout.LabelField("Utility Functions", EditorStyles.boldLabel);

                EditorGUILayout.BeginHorizontal();

                // Reset PlayerPrefs button
                if (GUILayout.Button("🔄 Reset PlayerPrefs", GUILayout.Height(25)))
                {
                    ResetPlayerPrefs();
                }

                // Set Test Score button
                if (GUILayout.Button("📊 Set Test Score", GUILayout.Height(25)))
                {
                    SetTestScore();
                }

                EditorGUILayout.EndHorizontal();

                EditorGUI.indentLevel--;
            }
        }

        #region Test Methods

        private void TriggerWinGame()
        {
            if (_gameController == null) return;

            // Set test score if needed
            if (_testNewHighScore)
            {
                int currentHighScore = PlayerPrefs.GetInt(Consts.PREFS_HIGHSCORE, 0);
                _gameController.Score = Mathf.Max(_testScore, currentHighScore + 100);
            }
            

            // Set PlayerPrefs for victory
            PlayerPrefs.SetInt(Consts.PREFS_LAST_WON, 1);
            PlayerPrefs.SetInt(Consts.PREFS_LAST_SCORE, _gameController.Score);

            Debug.Log($"[GameControllerEditor] Triggering WIN game with score: {_gameController.Score}");

            // Load EndGame scene directly
            LoadEndGameScene();
        }

        private void TriggerLoseGame()
        {
            if (_gameController == null) return;

            // Set test score
            _gameController.Score = _testScore;

            // Set PlayerPrefs for defeat
            PlayerPrefs.SetInt(Consts.PREFS_LAST_WON, 0);
            PlayerPrefs.SetInt(Consts.PREFS_LAST_SCORE, _gameController.Score);

            Debug.Log($"[GameControllerEditor] Triggering LOSE game with score: {_gameController.Score}");

            // Load EndGame scene directly
            LoadEndGameScene();
        }

        private void LoadEndGameScene()
        {
            Debug.Log("[GameControllerEditor] Loading EndGame scene");

            // Use the proper transition method like LoadSceneExecutable
            if (SceneTransitionServiceLocator.IsAvailable)
            {
                var transitionService = SceneTransitionServiceLocator.Instance;
                if (transitionService != null)
                {
                    Debug.Log($"[GameControllerEditor] Transitioning to scene: {Consts.SCENE_END_GAME}");
                    transitionService.TransitionToScene(Consts.SCENE_END_GAME, 1f);
                }
                else
                {
                    Debug.LogWarning("[GameControllerEditor] Transition service was null, using fallback");
                    FallbackSceneLoad(Consts.SCENE_END_GAME);
                }
            }
            else
            {
                Debug.Log($"[GameControllerEditor] Using fallback scene loading for: {Consts.SCENE_END_GAME}");
                FallbackSceneLoad(Consts.SCENE_END_GAME);
            }
        }

        private void LoadMainMenuScene()
        {
            Debug.Log("[GameControllerEditor] Loading Main Menu scene");

            // Use the proper transition method like LoadSceneExecutable
            if (SceneTransitionServiceLocator.IsAvailable)
            {
                var transitionService = SceneTransitionServiceLocator.Instance;
                if (transitionService != null)
                {
                    Debug.Log($"[GameControllerEditor] Transitioning to scene: {Consts.SCENE_MAIN_MENU}");
                    transitionService.TransitionToScene(Consts.SCENE_MAIN_MENU, 1f);
                }
                else
                {
                    Debug.LogWarning("[GameControllerEditor] Transition service was null, using fallback");
                    FallbackSceneLoad(Consts.SCENE_MAIN_MENU);
                }
            }
            else
            {
                Debug.Log($"[GameControllerEditor] Using fallback scene loading for: {Consts.SCENE_MAIN_MENU}");
                FallbackSceneLoad(Consts.SCENE_MAIN_MENU);
            }
        }

        private void ResetPlayerPrefs()
        {
            PlayerPrefs.DeleteKey(Consts.PREFS_HIGHSCORE);
            PlayerPrefs.DeleteKey(Consts.PREFS_LAST_SCORE);
            PlayerPrefs.DeleteKey(Consts.PREFS_LAST_WON);
            PlayerPrefs.Save();

            Debug.Log("[GameControllerEditor] PlayerPrefs reset");
            EditorUtility.DisplayDialog("PlayerPrefs Reset", "All game PlayerPrefs have been reset.", "OK");
        }

        private void SetTestScore()
        {
            if (_gameController == null) return;

            _gameController.Score = _testScore;
            Debug.Log($"[GameControllerEditor] Set game score to: {_testScore}");
        }

        private void FallbackSceneLoad(string sceneName)
        {
            // Fallback to direct scene loading (same as LoadSceneExecutable)
            Debug.Log($"[GameControllerEditor] Using fallback scene loading for: {sceneName}");
            UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName);
        }

        #endregion
    }
}