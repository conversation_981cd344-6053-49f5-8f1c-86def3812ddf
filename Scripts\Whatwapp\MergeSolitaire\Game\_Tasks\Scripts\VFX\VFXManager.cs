using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using _Tasks.Events;
using Whatwapp.Core.Audio;
using Whatwapp.MergeSolitaire.Game;

namespace _Tasks.NewFeature.VFX
{
    /// <summary>
    /// Centralized VFX Manager for handling explosion effects, screen shake, and particle systems
    /// Provides clear and impactful visual feedback for bomb explosions and block destruction
    /// </summary>
    public class VFXManager : MonoBehaviour
    {
        [Header("General")]
        [SerializeField] private Transform _particleParentTransform;
        
        [Header("Explosion Effects")]
        [SerializeField] private GameObject _explosionParticlePrefab;
        [SerializeField] private GameObject _subExplosionParticlePrefab;
        [SerializeField] private float _mainExplosionDuration = 0.5f;
        [SerializeField] private float _subExplosionDelay = 0.1f;
        [SerializeField] private float _subExplosionSpread = 0.05f;
        
        [Header("Screen Shake")]
        [SerializeField] private float _explosionShakeIntensity = 0.3f;
        [SerializeField] private float _explosionShakeDuration = 0.4f;
        [SerializeField] private int _explosionShakeVibrato = 10;
        [SerializeField] private float _explosionShakeRandomness = 90f;
        
        [Header("Block Destruction Effects")]
        [SerializeField] private Color _destructionFlashColor = Color.white;
        [SerializeField] private float _destructionFlashDuration = 0.15f;
        [SerializeField] private float _destructionScaleMultiplier = 1.3f;
        [SerializeField] private float _destructionFadeDuration = 0.2f;

        [Header("Score Effects")]
        [SerializeField] private GameObject _scoreUpdateParticlePrefab;
        [SerializeField] private GameObject _scoreMilestoneParticlePrefab;
        [SerializeField] private float _scoreParticleDuration = 1f;
        [SerializeField] private float _milestoneParticleDuration = 2f;

        [Header("Arrival Effects")]
        [SerializeField] private GameObject _blockArrivalParticlePrefab;
        [SerializeField] private GameObject _specialBlockArrivalParticlePrefab;
        [SerializeField] private float _arrivalParticleDuration = 0.8f;
        [SerializeField] private float _arrivalShakeIntensity = 0.15f;
        [SerializeField] private float _arrivalShakeDuration = 0.2f;
        [SerializeField] private float _specialArrivalShakeMultiplier = 2f;
        
        [Header("Audio")]
        [SerializeField] private string _explosionSfxName = Consts.SFX_BombExplosion;
        [SerializeField] private string _subExplosionSfxName = Consts.SFX_BlockDestroy;
        [SerializeField] private float _explosionVolume = 0.8f;
        [SerializeField] private float _subExplosionVolume = 0.6f;
        
        [Header("References")]
        [SerializeField] private Camera _mainCamera;
        [SerializeField] private AnimationSettings _animationSettings;
        
        [Header("Debug")]
        [SerializeField] private bool _debugMode = false;

        private static VFXManager _instance;
        public static VFXManager Instance => _instance;

        private SFXManager _sfxManager;
        private List<Coroutine> _activeEffects = new List<Coroutine>();

        // Flag to track milestone state and prevent normal score effects during milestone
        private bool _reachMilestone = false;

        #region Unity Lifecycle

        private void Awake()
        {
            // Singleton pattern
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
                return;
            }

            // Auto-find references if not assigned
            if (_mainCamera == null)
                _mainCamera = Camera.main;

            _sfxManager = SFXManager.Instance;

            // Subscribe to explosion events
            EventManager.Subscribe<BombExplosionEvent>(OnBombExplosion);
            EventManager.Subscribe<BlockDestructionRequestEvent>(OnBlockDestructionRequested);
            EventManager.Subscribe<BombDestructionMarkedEvent>(OnBombDestructionMarked);

            // Subscribe to score events
            EventManager.Subscribe<ScoreUpdateEvent>(OnScoreUpdate);
            EventManager.Subscribe<ScoreMilestoneEvent>(OnScoreMilestone);

            // Subscribe to arrival events
            EventManager.Subscribe<BlockArrivalEvent>(OnBlockArrival);
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            EventManager.Unsubscribe<BombExplosionEvent>(OnBombExplosion);
            EventManager.Unsubscribe<BlockDestructionRequestEvent>(OnBlockDestructionRequested);
            EventManager.Unsubscribe<BombDestructionMarkedEvent>(OnBombDestructionMarked);
            EventManager.Unsubscribe<ScoreUpdateEvent>(OnScoreUpdate);
            EventManager.Unsubscribe<ScoreMilestoneEvent>(OnScoreMilestone);
            EventManager.Unsubscribe<BlockArrivalEvent>(OnBlockArrival);

            // Stop all active effects
            StopAllEffects();
        }

        private void OnValidate()
        {
            // Validate references in editor
            if (_mainCamera == null)
            {
                _mainCamera = Camera.main;
                if (_mainCamera == null && Application.isPlaying)
                {
                    Debug.LogWarning("[VFXManager] Main camera reference is missing!");
                }
            }

            // Validate singleton instance
            if (Application.isPlaying && _instance != null && _instance != this)
            {
                Debug.LogWarning("[VFXManager] Multiple VFXManager instances detected! Only one should exist.");
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle bomb explosion events with main explosion effect and screen shake only
        /// </summary>
        private void OnBombExplosion(BombExplosionEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Processing bomb explosion at {eventData.ExplosionCenter}");

            // Play main explosion effect
            PlayMainExplosionEffect(eventData.ExplosionCenter, eventData.ExplosionRadius);

            // Trigger screen shake
            TriggerScreenShake(_explosionShakeIntensity, _explosionShakeDuration);

            // Play explosion sound
            PlayExplosionSound();
        }

        /// <summary>
        /// Handle bomb destruction marked events - trigger sub-explosions with proper timing
        /// </summary>
        private void OnBombDestructionMarked(BombDestructionMarkedEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Bomb destruction marked for {eventData.BlockPositions.Count} blocks");

            // Start sub-explosion coroutine with the captured block positions
            if (eventData.BlockPositions != null && eventData.BlockPositions.Count > 0)
            {
                StartCoroutine(PlaySubExplosionsCoroutine(eventData.BlockPositions));
            }
        }

        /// <summary>
        /// Handle block destruction requests with visual feedback
        /// </summary>
        private void OnBlockDestructionRequested(BlockDestructionRequestEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Processing block destruction request for {eventData.BlocksToDestroy.Count} blocks");

            // Apply destruction effects to each block before they get destroyed
            foreach (var block in eventData.BlocksToDestroy)
            {
                if (block != null && block.Visual != null)
                {
                    ApplyBlockDestructionEffect(block);
                }
            }
        }

        /// <summary>
        /// Handle score update events with particle effects
        /// </summary>
        private void OnScoreUpdate(ScoreUpdateEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Score updated from {eventData.PreviousScore} to {eventData.NewScore}, HasMilestone: {eventData.HasMilestone}, ReachMilestone: {_reachMilestone}");

            // Set milestone flag if this update includes a milestone
            if (eventData.HasMilestone)
            {
                _reachMilestone = true;
                if (_debugMode)
                    Debug.Log("[VFXManager] Milestone flag set to true - normal score effects will be suppressed");
                return;
            }

            // Skip normal score particles if we're currently in milestone state
            // This prevents normal score effects from interfering with milestone effects
            if (_reachMilestone)
            {
                if (_debugMode)
                    Debug.Log("[VFXManager] Skipping normal score particles due to active milestone state");
                return;
            }

            if (eventData.IsAnimated)
            {
                PlayScoreUpdateParticles(eventData.ScorePosition);
            }
        }

        /// <summary>
        /// Handle score milestone events with special particle effects
        /// </summary>
        private void OnScoreMilestone(ScoreMilestoneEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Score milestone achieved: {eventData.MilestoneScore}");

            PlayScoreMilestoneParticles(eventData.ScorePosition, eventData.MilestoneLevel);

            // Reset milestone flag after milestone effect duration to allow normal score effects again
            StartCoroutine(ResetMilestoneFlagAfterDelay());
        }

        /// <summary>
        /// Handle block arrival events with enhanced particle effects (no screen shake)
        /// </summary>
        private void OnBlockArrival(BlockArrivalEvent eventData)
        {
            if (_debugMode)
                Debug.Log($"[VFXManager] Block arrived at {eventData.ArrivalPosition}, Special: {eventData.IsSpecialBlock}, FromMerge: {eventData.IsFromMerge}");

            // Skip arrival particles for blocks created from merge operations
            // Merge operations have their own visual effects and don't need additional arrival particles
            if (eventData.IsFromMerge)
            {
                if (_debugMode)
                    Debug.Log("[VFXManager] Skipping arrival particles for merge-created block");
                return;
            }

            // Play enhanced arrival particles for normal arrivals
            if (eventData.TriggerParticles)
            {
                PlayEnhancedBlockArrivalParticles(eventData.ArrivalPosition, eventData.IsSpecialBlock, eventData.ImpactIntensity);
            }

            // Screen shake removed for cleaner visual experience
            // Focus on particle effects and visual feedback instead
        }

        #endregion

        #region Explosion Effects

        /// <summary>
        /// Play the main explosion effect at the bomb location
        /// </summary>
        private void PlayMainExplosionEffect(Vector3 position, int radius)
        {
            if (_explosionParticlePrefab != null)
            {
                var explosion = Instantiate(_explosionParticlePrefab, position,  _explosionParticlePrefab.transform.rotation,_particleParentTransform);
                
                // Scale particle system based on explosion radius
                var particleSystem = explosion.GetComponent<ParticleSystem>();
                if (particleSystem != null)
                {
                    var main = particleSystem.main;
                    main.startSize = main.startSize.constant * (1f + radius * 0.2f);
                }
                
                // Auto-destroy after duration
                Destroy(explosion, _mainExplosionDuration);
                
                if (_debugMode)
                    Debug.Log($"[VFXManager] Main explosion effect spawned at {position}");
            }
        }

        /// <summary>
        /// Play sub-explosion effects at captured positions with staggered timing
        /// </summary>
        private IEnumerator PlaySubExplosionsCoroutine(List<Vector3> blockPositions)
        {
            for (int i = 0; i < blockPositions.Count; i++)
            {
                // Add slight random delay for more natural effect
                float delay = i * _subExplosionDelay + Random.Range(0f, _subExplosionSpread);
                yield return new WaitForSeconds(delay);

                PlaySubExplosionEffect(blockPositions[i]);
                PlaySubExplosionSound();
            }
        }

        /// <summary>
        /// Play individual sub-explosion effect at block position
        /// </summary>
        private void PlaySubExplosionEffect(Vector3 position)
        {
            if (_subExplosionParticlePrefab != null)
            {
                var subExplosion = Instantiate(_subExplosionParticlePrefab, position, _subExplosionParticlePrefab.transform.rotation,_particleParentTransform);
                
                // Auto-destroy after a short duration
                Destroy(subExplosion, 1f);
                
                if (_debugMode)
                    Debug.Log($"[VFXManager] Sub-explosion effect spawned at {position}");
            }
        }

        #endregion

        #region Screen Shake

        /// <summary>
        /// Trigger camera screen shake effect
        /// </summary>
        public void TriggerScreenShake(float intensity = -1f, float duration = -1f)
        {
            if (_mainCamera == null) return;

            // Use default values if not specified
            if (intensity < 0) intensity = _explosionShakeIntensity;
            if (duration < 0) duration = _explosionShakeDuration;

            // Apply screen shake using DOTween
            _mainCamera.transform.DOShakePosition(
                duration,
                intensity,
                _explosionShakeVibrato,
                _explosionShakeRandomness,
                false,
                true
            );

            if (_debugMode)
                Debug.Log($"[VFXManager] Screen shake triggered - Intensity: {intensity}, Duration: {duration}");
        }

        /// <summary>
        /// Trigger a sub-explosion effect at a specific position
        /// </summary>
        public void TriggerSubExplosionAt(Vector3 position)
        {
            PlaySubExplosionEffect(position);
            PlaySubExplosionSound();

            if (_debugMode)
                Debug.Log($"[VFXManager] Sub-explosion triggered at {position}");
        }

        #endregion

        #region Block Destruction Effects

        /// <summary>
        /// Apply visual destruction effect to a specific block
        /// </summary>
        private void ApplyBlockDestructionEffect(Block block)
        {
            if (block?.Visual == null) return;

            var visual = block.Visual;
            var spriteRenderer = visual.GetComponent<SpriteRenderer>();
            
            if (spriteRenderer != null)
            {
                // Flash effect
                var originalColor = spriteRenderer.color;
                var flashSequence = DOTween.Sequence();
                
                flashSequence.Append(spriteRenderer.DOColor(_destructionFlashColor, _destructionFlashDuration * 0.5f))
                    .Append(spriteRenderer.DOColor(originalColor, _destructionFlashDuration * 0.5f));
                
                // Scale effect
                var originalScale = visual.transform.localScale;
                visual.transform.DOScale(originalScale * _destructionScaleMultiplier, _destructionFlashDuration)
                    .OnComplete(() => {
                        // Fade out effect
                        spriteRenderer.DOFade(0f, _destructionFadeDuration);
                        visual.transform.DOScale(originalScale * 0.8f, _destructionFadeDuration);
                    });
            }
        }

        #endregion

        #region Audio Effects

        /// <summary>
        /// Play main explosion sound effect
        /// </summary>
        private void PlayExplosionSound()
        {
            if (_sfxManager != null && !string.IsNullOrEmpty(_explosionSfxName))
            {
                _sfxManager.PlayOneShot(_explosionSfxName, _explosionVolume);
            }
        }

        /// <summary>
        /// Play sub-explosion sound effect
        /// </summary>
        private void PlaySubExplosionSound()
        {
            if (_sfxManager != null && !string.IsNullOrEmpty(_subExplosionSfxName))
            {
                _sfxManager.PlayOneShot(_subExplosionSfxName, _subExplosionVolume);
            }
        }

        #endregion

        #region Score Effects

        /// <summary>
        /// Play particle effects for score updates
        /// </summary>
        private void PlayScoreUpdateParticles(Vector3 position)
        {
            if (_scoreUpdateParticlePrefab != null)
            {
                var particles = Instantiate(_scoreUpdateParticlePrefab, position, _scoreUpdateParticlePrefab.transform.rotation,_particleParentTransform);

                // Auto-destroy after duration
                Destroy(particles, _scoreParticleDuration);

                if (_debugMode)
                    Debug.Log($"[VFXManager] Score update particles spawned at {position}");
            }
        }

        /// <summary>
        /// Play special particle effects for score milestones
        /// </summary>
        private void PlayScoreMilestoneParticles(Vector3 position, int milestoneLevel)
        {
            if (_scoreMilestoneParticlePrefab != null)
            {
                var particles = Instantiate(_scoreMilestoneParticlePrefab, position, _scoreMilestoneParticlePrefab.transform.rotation,_particleParentTransform);

                // Scale particle system based on milestone level
                var particleSystem = particles.GetComponent<ParticleSystem>();
                if (particleSystem != null)
                {
                    var main = particleSystem.main;
                    float scaleMultiplier = 1f + milestoneLevel / 1000f * 0.2f; // Bigger effects for higher milestones
                    main.startSize = main.startSize.constant * scaleMultiplier;
                }

                // Auto-destroy after duration
                Destroy(particles, _milestoneParticleDuration);

                if (_debugMode)
                    Debug.Log($"[VFXManager] Milestone particles spawned at {position} for level {milestoneLevel}");
            }
        }

        /// <summary>
        /// Public method to trigger score update particles manually
        /// </summary>
        public void TriggerScoreUpdateEffect(Vector3 position)
        {
            PlayScoreUpdateParticles(position);
        }

        /// <summary>
        /// Public method to trigger milestone particles manually
        /// </summary>
        public void TriggerMilestoneEffect(Vector3 position, int milestoneLevel)
        {
            PlayScoreMilestoneParticles(position, milestoneLevel);
            // Reset milestone flag after milestone effect duration
            StartCoroutine(ResetMilestoneFlagAfterDelay());
        }

        /// <summary>
        /// Public method to manually reset the milestone flag
        /// </summary>
        public void ResetMilestoneFlag()
        {
            _reachMilestone = false;
            if (_debugMode)
                Debug.Log("[VFXManager] Milestone flag manually reset to false");
        }

        /// <summary>
        /// Public property to check if milestone state is active
        /// </summary>
        public bool IsMilestoneActive => _reachMilestone;

        /// <summary>
        /// Coroutine to reset milestone flag after milestone effect duration
        /// </summary>
        private IEnumerator ResetMilestoneFlagAfterDelay()
        {
            yield return new WaitForSeconds(_milestoneParticleDuration);
            _reachMilestone = false;

            if (_debugMode)
                Debug.Log("[VFXManager] Milestone flag reset to false - normal score effects can resume");
        }

        #endregion

        #region Arrival Effects

        /// <summary>
        /// Play enhanced particle effects for block arrivals with multiple layers
        /// </summary>
        private void PlayEnhancedBlockArrivalParticles(Vector3 position, bool isSpecialBlock, float intensity)
        {
            GameObject prefabToUse = isSpecialBlock ? _specialBlockArrivalParticlePrefab : _blockArrivalParticlePrefab;

            if (prefabToUse != null)
            {
                var particles = Instantiate(prefabToUse, position, prefabToUse.transform.rotation,_particleParentTransform);

                // Enhanced particle system configuration
                var particleSystem = particles.GetComponent<ParticleSystem>();
                if (particleSystem != null)
                {
                    var main = particleSystem.main;
                    var emission = particleSystem.emission;
                    var shape = particleSystem.shape;
                    var velocityOverLifetime = particleSystem.velocityOverLifetime;

                    // Dynamic scaling based on intensity
                    float scaleMultiplier = 0.7f + intensity * 0.6f; // More dramatic scaling
                    main.startSize = main.startSize.constant * scaleMultiplier;

                    // Burst emission for more impact
                    emission.SetBursts(new ParticleSystem.Burst[]
                    {
                        new ParticleSystem.Burst(0.0f, (short)(10 + intensity * 15))
                    });

                    // Enhanced effects for special blocks
                    if (isSpecialBlock)
                    {
                        main.startColor = Color.Lerp(main.startColor.color, Color.yellow, 0.4f);
                        emission.rateOverTime = emission.rateOverTime.constant * 2f;

                        // Add secondary particle burst for special blocks
                        var secondaryBurst = new ParticleSystem.Burst(0.1f, (short)(intensity * 10));
                        emission.SetBursts(new ParticleSystem.Burst[]
                        {
                            new ParticleSystem.Burst(0.0f, (short)(15 + intensity * 20)),
                            secondaryBurst
                        });
                    }
                }

                // Auto-destroy after duration
                Destroy(particles, _arrivalParticleDuration);

                if (_debugMode)
                    Debug.Log($"[VFXManager] Enhanced arrival particles spawned at {position}, Special: {isSpecialBlock}, Intensity: {intensity}");
            }
        }

        /// <summary>
        /// Public method to trigger enhanced arrival effects manually (no screen shake)
        /// </summary>
        public void TriggerBlockArrivalEffect(Vector3 position, bool isSpecialBlock = false, float intensity = 1f, bool triggerShake = false)
        {
            PlayEnhancedBlockArrivalParticles(position, isSpecialBlock, intensity);

            // Screen shake removed for cleaner experience
            // triggerShake parameter kept for backward compatibility but defaults to false
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Stop all active visual effects
        /// </summary>
        public void StopAllEffects()
        {
            foreach (var effect in _activeEffects)
            {
                if (effect != null)
                {
                    StopCoroutine(effect);
                }
            }
            _activeEffects.Clear();
        }

        /// <summary>
        /// Check if VFX system is ready
        /// </summary>
        public bool IsReady()
        {
            return _mainCamera != null && _sfxManager != null;
        }

        #endregion
    }
}
