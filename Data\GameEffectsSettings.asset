%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0bd726c5cd96c4745926aada97909029, type: 3}
  m_Name: GameEffectsSettings
  m_EditorClassIdentifier: 
  _scoreFeedback:
    floatingTextSize: 24
    floatingTextColor: {r: 0, g: 0.8301887, b: 0.2580811, a: 1}
    floatingDistance: 2
    floatingDuration: 1.5
    floatingEase: 12
    counterAnimationDuration: 0.8
    counterEase: 12
    counterStepDelay: 0.02
    milestoneScaleDuration: 0.5
    milestoneScaleMultiplier: 1.2
    milestoneHighlightColor: {r: 0.9716981, g: 0.8376204, b: 0, a: 1}
    milestoneGlowDuration: 1
    milestonePulseDuration: 0.6
    moveToScoreDuration: 1.2
    moveToScoreEase: 13
    arrivalScaleEffect: 1.3
    arrivalScaleDuration: 0.3
  _mergeAnimation:
    scaleDownDuration: 0.5
    scaleUpDuration: 0.5
    scaleEase: 19
    finalScaleMultiplier: 1
    enableParticles: 1
    enableFlash: 1
    flashColor: {r: 1, g: 1, b: 1, a: 1}
    flashDuration: 0.2
  _cellSpawn:
    duration: 0.5
    scaleEase: 27
    delay: 0.05
    staggered: 1
    enableFadeIn: 1
    fadeInDuration: 0.3
    fadeEase: 12
  _destinationArrival:
    enableScalePulse: 1
    pulseScale: 0.5
    pulseDuration: 0.3
    pulseEase: 24
    enableGlow: 1
    glowColor: {r: 0, g: 1, b: 1, a: 1}
    glowDuration: 0.5
    impactScaleMultiplier: 1
    impactDuration: 0.1
