# 02 Lightning Block - Implementation Guide

## Overview

This guide provides step-by-step instructions for creating a Lightning special block that destroys 2-8 random blocks across the board with lightning effects after placement. The implementation follows the same event-driven architecture as the Bomb Block.

## Step-by-Step Implementation

### Step 1: Create Lightning Block Events

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Events\Events.cs`

Add these new event structures to the Events.cs file:

```csharp
/// <summary>
/// Event triggered when lightning block is placed
/// </summary>
public struct LightningPlacedEvent
{
    public LightningBlock LightningBlock;
    public Vector2Int Position;
    public float StrikeDelay;
    public int TargetCount;
}

/// <summary>
/// Event triggered when lightning movement stops
/// </summary>
public struct LightningMovementStoppedEvent
{
    public LightningBlock LightningBlock;
    public Vector2Int FinalPosition;
    public List<Block> TargetBlocks;
}

/// <summary>
/// Event triggered when lightning strikes occur
/// </summary>
public struct LightningStrikeEvent
{
    public Vector3 SourcePosition;
    public List<Block> TargetBlocks;
    public List<Vector3> TargetPositions;
    public LightningBlock SourceLightning;
    public float StrikeDuration;
}

/// <summary>
/// Event for VFX synchronization of lightning strikes
/// </summary>
public struct LightningStrikeMarkedEvent
{
    public List<Vector3> TargetPositions;
    public Vector3 SourcePosition;
    public float ExpectedStrikeDelay;
    public int StrikeCount;
}
```

### Step 2: Update BlockType Enum

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\BlockType.cs`

```csharp
namespace Whatwapp.MergeSolitaire.Shared
{
    public enum BlockType
    {
        Normal,
        Bomb,
        Lightning  // Add this new type
    }
}
```

### Step 3: Create Lightning Block Class

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Block\LightningBlock.cs`

```csharp
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using _Tasks.Events;
using _Tasks.NewFeature.Controllers;
using _Tasks.NewFeature.Shared;
using UnityEngine;
using Whatwapp.MergeSolitaire.Game;
using Whatwapp.MergeSolitaire.Shared;

namespace _Tasks.NewFeature
{
    public class LightningBlock : Block, ISpecialBlock
    {
        [Header("Lightning Settings")]
        [SerializeField] private float _strikeDelay = 0.8f;
        [SerializeField] private int _minTargets = 2;
        [SerializeField] private int _maxTargets = 8;
        [SerializeField] private bool _destroysSelf = true;
        [SerializeField] private bool _debugMode = false;

        public BlockType Type => BlockType.Lightning;
        public bool CanMerge => false; // Lightning blocks don't merge

        private bool _hasStruck = false;
        private Coroutine _strikeCoroutine;
        private bool _isPlaced = false;
        private bool _hasMovementStopped = false;

        public override void Init(BlockValue value, BlockSeed seed)
        {
            base.Init(value, seed);

            if (_debugMode)
                Debug.Log($"[LightningBlock] Initialized at position {transform.position}");
        }

        public void OnPlacementComplete()
        {
            _isPlaced = true;
            var currentPos = GetCurrentCellPosition();
            var targetCount = Random.Range(_minTargets, _maxTargets + 1);

            // Broadcast lightning placed event
            var lightningPlacedEvent = new LightningPlacedEvent
            {
                LightningBlock = this,
                Position = currentPos,
                StrikeDelay = _strikeDelay,
                TargetCount = targetCount
            };

            EventManager.Broadcast(lightningPlacedEvent);

            // Only start strike timer if we're not going to move
            if (ShouldStartStrikeTimer())
            {
                StartStrikeTimer();
            }

            if (_debugMode)
                Debug.Log($"[LightningBlock] Placed at position {currentPos}, targets: {targetCount}");
        }

        public void OnMovementStopped()
        {
            if (_hasStruck) return;

            _hasMovementStopped = true;
            var currentPos = GetCurrentCellPosition();
            var targetBlocks = GetRandomTargetBlocks();

            // Broadcast movement stopped event
            var movementStoppedEvent = new LightningMovementStoppedEvent
            {
                LightningBlock = this,
                FinalPosition = currentPos,
                TargetBlocks = targetBlocks
            };

            EventManager.Broadcast(movementStoppedEvent);

            // Only start strike timer if we haven't already started one
            if (_strikeCoroutine == null)
            {
                StartStrikeTimer();
            }

            if (_debugMode)
                Debug.Log($"[LightningBlock] Movement stopped, targeting {targetBlocks.Count} blocks");
        }

        private bool ShouldStartStrikeTimer()
        {
            if (SpecialBlockController.Instance == null) return true;

            var currentPos = GetCurrentCellPosition();
            var board = SpecialBlockController.Instance.GetBoard();

            if (board == null) return true;

            // Check if we're at the bottom row
            if (currentPos.y == 0) return true;

            // Check if there's a block below us
            var cellBelow = board.GetCell(currentPos.x, currentPos.y - 1);
            if (cellBelow != null && !cellBelow.IsEmpty) return true;

            return false;
        }

        private void StartStrikeTimer()
        {
            if (_strikeCoroutine != null)
            {
                StopCoroutine(_strikeCoroutine);
            }
            _strikeCoroutine = StartCoroutine(StrikeAfterDelay());
        }

        private IEnumerator StrikeAfterDelay()
        {
            yield return new WaitForSeconds(_strikeDelay);

            if (!_hasStruck)
            {
                Strike();
            }
        }

        private void Strike()
        {
            _hasStruck = true;

            var targetBlocks = GetRandomTargetBlocks();
            var targetPositions = new List<Vector3>();

            foreach (var block in targetBlocks)
            {
                if (block != null && block.transform != null)
                {
                    targetPositions.Add(block.transform.position);
                }
            }

            if (_debugMode)
                Debug.Log($"[LightningBlock] Striking {targetBlocks.Count} random blocks");

            // Broadcast lightning strike marked event for VFX synchronization
            var strikeMarkedEvent = new LightningStrikeMarkedEvent
            {
                TargetPositions = targetPositions,
                SourcePosition = transform.position,
                ExpectedStrikeDelay = 0f,
                StrikeCount = targetBlocks.Count
            };
            EventManager.Broadcast(strikeMarkedEvent);

            // Broadcast lightning strike event for immediate VFX
            var strikeEvent = new LightningStrikeEvent
            {
                SourcePosition = transform.position,
                TargetBlocks = targetBlocks,
                TargetPositions = targetPositions,
                SourceLightning = this,
                StrikeDuration = 0.5f
            };
            EventManager.Broadcast(strikeEvent);

            // Broadcast block destruction request
            if (targetBlocks.Count > 0)
            {
                var destructionEvent = new BlockDestructionRequestEvent
                {
                    BlocksToDestroy = targetBlocks,
                    ExplosionCenter = transform.position,
                    PlayDestructionEffects = true
                };

                EventManager.Broadcast(destructionEvent);
            }

            // Self-destruct after a brief delay
            StartCoroutine(SelfDestruct());
        }

        private List<Block> GetRandomTargetBlocks()
        {
            var targetBlocks = new List<Block>();
            
            if (SpecialBlockController.Instance == null)
            {
                Debug.LogError("[LightningBlock] SpecialBlockController instance not found!");
                return targetBlocks;
            }

            var board = SpecialBlockController.Instance.GetBoard();
            if (board == null) return targetBlocks;

            // Get all blocks on the board except this lightning block
            var allBlocks = new List<Block>();
            var boardSize = SpecialBlockController.Instance.GetBoardSize();

            for (int x = 0; x < boardSize.x; x++)
            {
                for (int y = 0; y < boardSize.y; y++)
                {
                    var cell = board.GetCell(x, y);
                    if (cell != null && !cell.IsEmpty && cell.Block != this)
                    {
                        allBlocks.Add(cell.Block);
                    }
                }
            }

            // Randomly select targets
            var targetCount = Random.Range(_minTargets, Mathf.Min(_maxTargets + 1, allBlocks.Count + 1));
            var shuffledBlocks = allBlocks.OrderBy(x => Random.value).Take(targetCount).ToList();

            return shuffledBlocks;
        }

        private Vector2Int GetCurrentCellPosition()
        {
            if (SpecialBlockController.Instance == null)
            {
                Debug.LogError("[LightningBlock] SpecialBlockController instance not found!");
                return Vector2Int.zero;
            }

            return SpecialBlockController.Instance.GetCellCoordinates(transform.position);
        }

        private IEnumerator SelfDestruct()
        {
            yield return new WaitForSeconds(0.3f);

            if (_debugMode)
                Debug.Log("[LightningBlock] Self-destructing");

            if (_destroysSelf)
            {
                var currentPos = GetCurrentCellPosition();
                if (SpecialBlockController.Instance != null)
                {
                    SpecialBlockController.Instance.DestroyBlockAtPosition(currentPos);
                }
                else
                {
                    Remove();
                }
            }
        }

        private void OnDestroy()
        {
            if (_strikeCoroutine != null)
            {
                StopCoroutine(_strikeCoroutine);
                _strikeCoroutine = null;
            }
        }

        // Public method for testing/debugging
        public void ForceStrike()
        {
            if (!_hasStruck)
            {
                if (_strikeCoroutine != null)
                {
                    StopCoroutine(_strikeCoroutine);
                }
                Strike();
            }
        }
    }
}
```

### Step 4: Update SpecialBlockController

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Controllers\SpecialBlockController.cs`

Add these event subscriptions in the `Awake()` method:

```csharp
// Add to existing event subscriptions
EventManager.Subscribe<LightningPlacedEvent>(OnLightningPlaced);
EventManager.Subscribe<LightningMovementStoppedEvent>(OnLightningMovementStopped);
```

Add these event handlers in the `#region Event Handlers` section:

```csharp
private void OnLightningPlaced(LightningPlacedEvent eventData)
{
    if (_debugMode)
        Debug.Log($"[SpecialBlockController] Lightning placed at {eventData.Position}");
}

private void OnLightningMovementStopped(LightningMovementStoppedEvent eventData)
{
    if (_debugMode)
        Debug.Log($"[SpecialBlockController] Lightning movement stopped, targeting {eventData.TargetBlocks.Count} blocks");
}
```

Don't forget to unsubscribe in `OnDestroy()`:

```csharp
EventManager.Unsubscribe<LightningPlacedEvent>(OnLightningPlaced);
EventManager.Unsubscribe<LightningMovementStoppedEvent>(OnLightningMovementStopped);
```

### Step 5: Update BlockFactory

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\BlockFactory.cs`

Add lightning block prefab field:

```csharp
[Header("Prefabs")]
[SerializeField] private Block _blockPrefab;
[SerializeField] private BombBlock _bombBlockPrefab;
[SerializeField] private LightningBlock _lightningBlockPrefab;  // Add this
```

Add lightning creation method:

```csharp
/// <summary>
/// Create a lightning block with specified settings
/// </summary>
public Block CreateLightningBlock(BlockValue value = BlockValue.Ace, BlockSeed seed = BlockSeed.Spades)
{
    if (_lightningBlockPrefab == null)
    {
        Debug.LogError("[BlockFactory] Lightning block prefab is not assigned!");
        return null;
    }

    var lightningBlock = Instantiate(_lightningBlockPrefab, this.transform);
    return lightningBlock;
}
```

Update the `CreateBlock` method:

```csharp
public Block CreateBlock(BlockType blockType, BlockValue value, BlockSeed seed)
{
    switch (blockType)
    {
        case BlockType.Bomb:
            return CreateBombBlock(value, seed);
        case BlockType.Lightning:  // Add this case
            return CreateLightningBlock(value, seed);
        case BlockType.Normal:
        default:
            return Create(value, seed);
    }
}
```

### Step 6: Update NextBlockController

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\NextBlockController.cs`

Add lightning spawn probability:

```csharp
[SerializeField] [Range(0f, 1f)] private float _probabilityToSpawnBombBlock = 0.05f;
[SerializeField] [Range(0f, 1f)] private float _probabilityToSpawnLightningBlock = 0.03f;  // Add this
```

Update the `ExtractNextBlock()` method:

```csharp
public void ExtractNextBlock()
{
    if (_nextBlock != null) return;
    var seed = EnumUtils.GetRandom<BlockSeed>();
    var value = EnumUtils.GetRandom<BlockValue>(BlockValue.Ace, BlockValue.King);

    // Check for special block generation first
    var randomValue = Random.value;
    
    if (randomValue < _probabilityToSpawnLightningBlock)
    {
        // Create lightning block
        _nextBlock = _blockFactory.CreateBlock(BlockType.Lightning, value, seed);
    }
    else if (randomValue < _probabilityToSpawnLightningBlock + _probabilityToSpawnBombBlock)
    {
        // Create bomb block
        _nextBlock = _blockFactory.CreateBlock(BlockType.Bomb, value, seed);
    }
    // ... rest of existing logic
}
```

### Step 7: Create Lightning Block Visual

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Visual\LightningBlockVisual.cs`

```csharp
using DG.Tweening;
using UnityEngine;
using TMPro;
using _Tasks.Events;

namespace _Tasks.NewFeature.Visual
{
    public class LightningBlockVisual : BlockVisual
    {
        [Header("Lightning Visual Settings")]
        [SerializeField] private Color _lightningColor = Color.cyan;
        [SerializeField] private float _pulseDuration = 0.8f;
        [SerializeField] private float _pulseIntensity = 0.15f;
        [SerializeField] private float _chargeUpDuration = 1.0f;

        [Header("Strike Visual")]
        [SerializeField] private Color _strikeColor = Color.white;
        [SerializeField] private float _strikeFlashDuration = 0.2f;
        [SerializeField] private float _strikeScaleMultiplier = 1.3f;

        private Sequence _pulseSequence;
        private Sequence _chargeSequence;
        private bool _isCharging = false;
        private bool _hasStruck = false;

        private Vector3 _originalScale;
        private Color _originalColor;

        protected virtual void Awake()
        {
            _spriteRenderer = GetComponentInChildren<SpriteRenderer>();
            _text = GetComponentInChildren<TextMeshPro>();

            if (_spriteRenderer != null)
            {
                _originalColor = _spriteRenderer.color;
            }

            _originalScale = transform.localScale;
        }

        protected virtual void Start()
        {
            // Subscribe to lightning events
            EventManager.Subscribe<LightningMovementStoppedEvent>(OnLightningMovementStopped);
            EventManager.Subscribe<LightningStrikeEvent>(OnLightningStrike);

            // Start idle pulse animation
            StartIdlePulse();
        }

        protected override void OnDestroy()
        {
            // Unsubscribe from events
            EventManager.Unsubscribe<LightningMovementStoppedEvent>(OnLightningMovementStopped);
            EventManager.Unsubscribe<LightningStrikeEvent>(OnLightningStrike);

            // Clean up tweens
            _pulseSequence?.Kill();
            _chargeSequence?.Kill();
        }

        private void StartIdlePulse()
        {
            if (_hasStruck || _isCharging) return;

            _pulseSequence?.Kill();
            _pulseSequence = DOTween.Sequence()
                .Append(transform.DOScale(_originalScale * (1f + _pulseIntensity), _pulseDuration * 0.5f))
                .Append(transform.DOScale(_originalScale, _pulseDuration * 0.5f))
                .SetLoops(-1, LoopType.Restart)
                .SetEase(Ease.InOutSine);
        }

        private void StartChargeVisual()
        {
            if (_hasStruck) return;

            _isCharging = true;

            // Stop idle pulse
            _pulseSequence?.Kill();

            // Start charge-up animation
            _chargeSequence?.Kill();
            _chargeSequence = DOTween.Sequence();

            // Rapid pulsing with increasing intensity
            if (_spriteRenderer != null)
            {
                _chargeSequence.Append(_spriteRenderer.DOColor(_lightningColor, _chargeUpDuration * 0.3f));
            }

            // Faster pulsing scale
            var chargePulse = DOTween.Sequence()
                .Append(transform.DOScale(_originalScale * 1.1f, 0.2f))
                .Append(transform.DOScale(_originalScale * 0.95f, 0.2f))
                .SetLoops(-1, LoopType.Restart)
                .SetEase(Ease.InOutQuart);

            _chargeSequence.Join(chargePulse);
        }

        private void PlayStrikeVisual()
        {
            if (_hasStruck) return;

            _hasStruck = true;

            // Stop all other animations
            _pulseSequence?.Kill();
            _chargeSequence?.Kill();

            // Flash bright and scale up briefly
            var strikeSequence = DOTween.Sequence();

            if (_spriteRenderer != null)
            {
                strikeSequence.Join(_spriteRenderer.DOColor(_strikeColor, _strikeFlashDuration));
            }

            strikeSequence.Join(transform.DOScale(_originalScale * _strikeScaleMultiplier, _strikeFlashDuration))
                .AppendCallback(() => {
                    // The block will be destroyed by the game logic
                });
        }

        #region Event Handlers

        private void OnLightningMovementStopped(LightningMovementStoppedEvent eventData)
        {
            // Check if this event is for this lightning block
            if (eventData.LightningBlock != null && eventData.LightningBlock.transform == transform.parent)
            {
                StartChargeVisual();
            }
        }

        private void OnLightningStrike(LightningStrikeEvent eventData)
        {
            // Check if this event is for this lightning block
            if (eventData.SourceLightning != null && eventData.SourceLightning.transform == transform.parent)
            {
                PlayStrikeVisual();
            }
        }

        #endregion
    }
}
```

### Step 8: Update VFXManager for Lightning Effects

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\VFX\VFXManager.cs`

Add lightning-specific fields to the VFXManager:

```csharp
[Header("Lightning Effects")]
[SerializeField] private GameObject _lightningStrikePrefab;
[SerializeField] private LineRenderer _lightningLineRenderer;
[SerializeField] private float _lightningStrikeDuration = 0.5f;
[SerializeField] private float _lightningFlashIntensity = 0.4f;
[SerializeField] private float _lightningFlashDuration = 0.3f;
```

Add lightning event subscriptions in `Start()`:

```csharp
// Add to existing event subscriptions
EventManager.Subscribe<LightningStrikeEvent>(OnLightningStrike);
EventManager.Subscribe<LightningStrikeMarkedEvent>(OnLightningStrikeMarked);
```

Add lightning event handlers:

```csharp
/// <summary>
/// Handle lightning strike events with visual effects
/// </summary>
private void OnLightningStrike(LightningStrikeEvent eventData)
{
    if (_debugMode)
        Debug.Log($"[VFXManager] Processing lightning strike from {eventData.SourcePosition} to {eventData.TargetPositions.Count} targets");

    // Trigger screen flash for lightning
    TriggerScreenShake(_lightningFlashIntensity, _lightningFlashDuration);

    // Start lightning strike coroutine
    StartCoroutine(PlayLightningStrikesCoroutine(eventData.SourcePosition, eventData.TargetPositions));
}

/// <summary>
/// Handle lightning strike marked events for VFX synchronization
/// </summary>
private void OnLightningStrikeMarked(LightningStrikeMarkedEvent eventData)
{
    if (_debugMode)
        Debug.Log($"[VFXManager] Lightning strike marked for {eventData.StrikeCount} targets");

    // Trigger immediate lightning effects at target positions
    foreach (var position in eventData.TargetPositions)
    {
        PlayLightningImpactEffect(position);
    }
}

/// <summary>
/// Play lightning strikes with staggered timing
/// </summary>
private IEnumerator PlayLightningStrikesCoroutine(Vector3 sourcePosition, List<Vector3> targetPositions)
{
    const float strikeDelay = 0.1f;

    for (int i = 0; i < targetPositions.Count; i++)
    {
        // Small delay between strikes for visual effect
        if (i > 0) yield return new WaitForSeconds(strikeDelay);

        // Draw lightning line from source to target
        DrawLightningLine(sourcePosition, targetPositions[i]);

        // Play impact effect at target
        PlayLightningImpactEffect(targetPositions[i]);
    }
}

/// <summary>
/// Draw lightning line between two points
/// </summary>
private void DrawLightningLine(Vector3 from, Vector3 to)
{
    if (_lightningLineRenderer != null)
    {
        _lightningLineRenderer.positionCount = 2;
        _lightningLineRenderer.SetPosition(0, from);
        _lightningLineRenderer.SetPosition(1, to);
        _lightningLineRenderer.enabled = true;

        // Fade out the line after a short duration
        StartCoroutine(FadeLightningLine());
    }
}

/// <summary>
/// Fade out lightning line renderer
/// </summary>
private IEnumerator FadeLightningLine()
{
    yield return new WaitForSeconds(0.1f);

    if (_lightningLineRenderer != null)
    {
        _lightningLineRenderer.enabled = false;
    }
}

/// <summary>
/// Play lightning impact effect at target position
/// </summary>
private void PlayLightningImpactEffect(Vector3 position)
{
    if (_lightningStrikePrefab != null)
    {
        var impact = Instantiate(_lightningStrikePrefab, position, _lightningStrikePrefab.transform.rotation, _particleParentTransform);

        // Auto-destroy after duration
        Destroy(impact, _lightningStrikeDuration);

        if (_debugMode)
            Debug.Log($"[VFXManager] Lightning impact effect spawned at {position}");
    }
}
```

### Step 9: Update VFXState for Lightning

**File**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\GameState\VFXState.cs`

Add lightning event subscription in `OnEnter()`:

```csharp
// Add to existing event subscriptions
EventManager.Subscribe<LightningStrikeMarkedEvent>(OnLightningStrikeMarked);
```

Add lightning event handler:

```csharp
/// <summary>
/// Handle lightning strike marked events
/// </summary>
private void OnLightningStrikeMarked(LightningStrikeMarkedEvent eventData)
{
    if (!_isStateActive)
    {
        // Queue for later processing if needed
        return;
    }

    if (_isProcessingVFX) return;

    Debug.Log($"[VFXState] Lightning strike marked for {eventData.StrikeCount} targets");

    // Start VFX processing for lightning
    _vfxCoroutine = _gameController.StartCoroutine(ProcessLightningVFXCoroutine(eventData));
}

/// <summary>
/// Process lightning VFX with proper timing
/// </summary>
private IEnumerator ProcessLightningVFXCoroutine(LightningStrikeMarkedEvent eventData)
{
    _isProcessingVFX = true;

    // Wait for the expected strike delay
    yield return new WaitForSeconds(eventData.ExpectedStrikeDelay);

    // Lightning effects are handled by VFXManager, just wait for completion
    yield return new WaitForSeconds(0.5f); // Duration for lightning effects

    _isProcessingVFX = false;
    _vfxCompleted = true;

    Debug.Log("[VFXState] Lightning VFX processing completed");
}
```

Don't forget to unsubscribe in `OnExit()`:

```csharp
EventManager.Unsubscribe<LightningStrikeMarkedEvent>(OnLightningStrikeMarked);
```

### Step 10: Create Lightning Block Prefab

**Unity Editor Steps:**

1. **Create Base Prefab**:
   - Duplicate the existing Block prefab
   - Rename it to "LightningBlock"
   - Replace the Block component with LightningBlock component

2. **Setup Visual Component**:
   - Replace BlockVisual with LightningBlockVisual component
   - Configure lightning-specific colors and settings:
     - Lightning Color: Cyan (#00FFFF)
     - Pulse Duration: 0.8s
     - Pulse Intensity: 0.15
     - Charge Up Duration: 1.0s

3. **Configure Lightning Settings**:
   - Strike Delay: 0.8s
   - Min Targets: 2
   - Max Targets: 8
   - Destroys Self: true
   - Debug Mode: false (for production)

4. **Assign to BlockFactory**:
   - Select the BlockFactory GameObject
   - Assign the LightningBlock prefab to the `_lightningBlockPrefab` field

### Step 11: Create Lightning VFX Prefabs

**Unity Editor Steps:**

1. **Lightning Strike Particle Effect**:
   - Create new GameObject with Particle System
   - Configure for electric/lightning effect:
     - Shape: Cone with small angle
     - Emission: Burst of 20-50 particles
     - Color: Electric blue to white gradient
     - Size: Small to medium particles
     - Velocity: Upward burst with random spread

2. **Lightning Line Renderer**:
   - Create GameObject with LineRenderer component
   - Configure for lightning bolt:
     - Material: Bright electric material
     - Width: 0.1 units
     - Color: Bright cyan/white
     - Use World Space: true

3. **Assign to VFXManager**:
   - Select VFXManager GameObject
   - Assign lightning prefabs to respective fields

### Step 12: Testing and Validation

**Create Test Script** (`Scripts\_Tests\LightningBlockTests.cs`):

```csharp
using NUnit.Framework;
using UnityEngine;
using _Tasks.NewFeature;
using _Tasks.Events;

public class LightningBlockTests
{
    private LightningBlock _lightningBlock;
    private GameObject _testGameObject;

    [SetUp]
    public void Setup()
    {
        _testGameObject = new GameObject("TestLightningBlock");
        _lightningBlock = _testGameObject.AddComponent<LightningBlock>();
    }

    [TearDown]
    public void TearDown()
    {
        if (_testGameObject != null)
        {
            Object.DestroyImmediate(_testGameObject);
        }
    }

    [Test]
    public void LightningBlock_HasCorrectType()
    {
        Assert.AreEqual(BlockType.Lightning, _lightningBlock.Type);
    }

    [Test]
    public void LightningBlock_CannotMerge()
    {
        Assert.IsFalse(_lightningBlock.CanMerge);
    }

    [Test]
    public void LightningBlock_BroadcastsPlacementEvent()
    {
        bool eventReceived = false;

        EventManager.Subscribe<LightningPlacedEvent>(evt => eventReceived = true);

        _lightningBlock.OnPlacementComplete();

        Assert.IsTrue(eventReceived);

        EventManager.Unsubscribe<LightningPlacedEvent>(evt => eventReceived = true);
    }
}
```

**Manual Testing Checklist**:

1. ✅ Lightning block spawns with correct probability
2. ✅ Lightning block moves down like normal blocks
3. ✅ Lightning block triggers strike after movement stops
4. ✅ Random blocks (2-8) are destroyed across the board
5. ✅ Visual effects play correctly (pulse, charge, strike)
6. ✅ Lightning lines connect source to targets
7. ✅ Screen flash occurs during strike
8. ✅ Lightning block self-destructs after strike
9. ✅ Game state transitions correctly after lightning
10. ✅ No memory leaks or performance issues

### Step 13: Configuration and Balancing

**Recommended Settings**:

```csharp
// Lightning Block Settings
Strike Delay: 0.8f        // Time before strike after stopping
Min Targets: 2            // Minimum blocks to destroy
Max Targets: 8            // Maximum blocks to destroy
Destroys Self: true       // Lightning destroys itself

// Spawn Probability
Lightning Spawn Rate: 3%  // Lower than bomb (5%) due to higher impact

// Visual Settings
Pulse Duration: 0.8f      // Idle animation speed
Charge Duration: 1.0f     // Charge-up animation time
Strike Flash: 0.3f        // Screen flash duration
```

**Balancing Considerations**:
- Lightning is more powerful than bombs (affects entire board)
- Lower spawn rate compensates for higher impact
- Random targeting adds unpredictability
- Visual feedback clearly indicates lightning behavior

### Step 14: Performance Optimization

**Optimization Tips**:

1. **Object Pooling**: Pool lightning VFX objects for reuse
2. **Batch Operations**: Group multiple lightning strikes if possible
3. **LOD System**: Reduce particle quality at distance
4. **Memory Management**: Ensure proper cleanup of coroutines and events

**Performance Monitoring**:
```csharp
// Add to LightningBlock for performance tracking
private void Update()
{
    if (_debugMode && _hasStruck)
    {
        Debug.Log($"[Performance] Lightning processing time: {Time.time - _strikeStartTime}s");
    }
}
```

## Summary

This implementation guide provides a complete step-by-step process for creating a Lightning special block that:

- ✅ Follows the same architecture patterns as Bomb Block
- ✅ Destroys 2-8 random blocks across the entire board
- ✅ Provides clear visual feedback with lightning effects
- ✅ Integrates seamlessly with existing game systems
- ✅ Maintains performance and code quality standards

The Lightning Block demonstrates the extensibility of the special block system and serves as a template for creating additional special block types with different behaviors and effects.
