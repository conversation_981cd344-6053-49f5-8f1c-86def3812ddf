# Bomb Block System Documentation

## Overview
The Bomb Block system is a special block feature implemented using an event-driven architecture. It provides explosive functionality that destroys blocks within a specified radius when triggered. The system is designed to be modular, extensible, and decoupled from the core game mechanics.

## Architecture

### Core Components

#### 1. ISpecialBlock Interface
**Location**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Interfaces\ISpecialBlock.cs`

```csharp
public interface ISpecialBlock
{
    BlockType Type { get; }
    void OnPlacementComplete();
    void OnMovementStopped();
    bool CanMerge { get; }
}
```

**Purpose**: Defines the contract for all special blocks in the game.
**Key Properties**:
- `Type`: Identifies the block type (Normal, Bomb)
- `CanMerge`: Determines if the block can participate in merge operations
- `OnPlacementComplete()`: Called when block is initially placed
- `OnMovementStopped()`: Called when block stops moving/falling

#### 2. BombBlock Class
**Location**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Block\BombBlock.cs`

**Purpose**: Main implementation of the bomb functionality.

**Key Features**:
- Inherits from `Block` and implements `ISpecialBlock`
- Configurable explosion delay, radius, and self-destruction behavior
- Event-driven communication with other systems
- Smart explosion timing based on movement state

**Configuration Properties**:
```csharp
[SerializeField] private float _explosionDelay = 0.5f;
[SerializeField] private int _explosionRadius = 1;
[SerializeField] private bool _destroysSelf = true;
[SerializeField] private bool _debugMode = false;
```

**State Management**:
- `_hasExploded`: Prevents multiple explosions
- `_isPlaced`: Tracks placement state
- `_hasMovementStopped`: Tracks movement completion
- `_explosionCoroutine`: Manages explosion timing

#### 3. SpecialBlockController
**Location**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Controllers\SpecialBlockController.cs`

**Purpose**: Centralized controller providing services to special blocks without requiring direct board access.

**Key Responsibilities**:
- Board interaction abstraction
- Block destruction management
- Coordinate system utilities
- Event handling for special block operations

**Public API**:
- `GetCellCoordinates(Vector3 worldPosition)`: Convert world to grid coordinates
- `GetCell(Vector2Int coordinates)`: Get cell at specific position
- `GetCellsInRadius(Vector2Int centerPos, int radius)`: Get cells within radius
- `GetDestroyableBlocksInRadius()`: Get blocks that can be destroyed
- `DestroyBlockAtPosition()`: Safely destroy block at position
- `DestroyBlocks()`: Safely destroy multiple blocks

#### 4. VFXState
**Location**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\GameState\VFXState.cs`

**Purpose**: Game state responsible for synchronizing visual effects with block destruction.

**Key Features**:
- Handles VFX timing synchronization
- Processes bomb destruction events
- Manages sub-explosion effects
- Provides timeout mechanism for state transitions

#### 5. BlockFactory
**Location**: `Scripts\Whatwapp\MergeSolitaire\Game\BlockFactory.cs`

**Purpose**: Factory pattern implementation for creating different block types.

**Methods**:
- `CreateBombBlock()`: Creates bomb block instances
- `CreateBlock(BlockType blockType)`: Creates blocks based on type
- `Create()`: Creates normal blocks

## Event System

### Event Flow
1. **Placement**: `BombPlacedEvent` → Notifies placement completion
2. **Movement**: `BombMovementStoppedEvent` → Triggers explosion timer
3. **Explosion**: Multiple events fired simultaneously:
   - `BombDestructionMarkedEvent` → VFX synchronization
   - `BombExplosionEvent` → Main explosion effects
   - `BlockDestructionRequestEvent` → Block destruction
4. **Cleanup**: `BlocksDestroyedEvent` → Game state updates

### Event Definitions
**Location**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Events\Events.cs`

```csharp
// Core bomb events
public struct BombPlacedEvent
public struct BombMovementStoppedEvent  
public struct BombExplosionEvent
public struct BombDestructionMarkedEvent
public struct BlockDestructionRequestEvent
public struct BlocksDestroyedEvent
public struct VFXSyncEvent
```

### EventManager
**Location**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\Scripts\Events\EventManager.cs`

**Purpose**: Simple, type-safe event system using Unity Events.

**Key Methods**:
- `Subscribe<T>(UnityAction<T> listener)`: Subscribe to events
- `Unsubscribe<T>(UnityAction<T> listener)`: Unsubscribe from events  
- `Broadcast<T>(T context)`: Broadcast events to subscribers

## Data Types

### BlockType Enum
**Location**: `Scripts\Whatwapp\MergeSolitaire\Game\_Tasks\BlockType.cs`

```csharp
public enum BlockType
{
    Normal,
    Bomb
}
```

### Supporting Enums
- `BlockValue`: Card values (Ace through King)
- `BlockSeed`: Card suits (Clubs, Diamonds, Hearts, Spades)

## Workflow

### Bomb Lifecycle
1. **Creation**: Factory creates bomb block with prefab
2. **Placement**: `OnPlacementComplete()` called, broadcasts `BombPlacedEvent`
3. **Movement Check**: Determines if bomb will fall or stay in place
4. **Timer Start**: Explosion timer starts when movement stops
5. **Explosion**: After delay, bomb explodes destroying nearby blocks
6. **VFX Sync**: Visual effects synchronized with destruction
7. **Cleanup**: Bomb self-destructs and game state updates

### Smart Timing Logic
The bomb uses intelligent timing to handle different placement scenarios:

```csharp
private bool ShouldStartExplosionTimer()
{
    // Check if at bottom row
    if (currentPos.y == 0) return true;
    
    // Check if block below exists
    var cellBelow = board.GetCell(currentPos.x, currentPos.y - 1);
    if (cellBelow != null && !cellBelow.IsEmpty) return true;
    
    // Will move, don't start timer yet
    return false;
}
```

### Explosion Process
1. **Target Identification**: Find all destroyable blocks in radius
2. **Position Capture**: Store block positions for VFX
3. **Event Broadcasting**: Fire multiple events for different systems
4. **Block Destruction**: Use SpecialBlockController for safe cleanup
5. **Self Destruction**: Remove bomb after brief delay

## Integration Points

### Game Controller Integration
- VFXState handles bomb-related visual effects
- Game state transitions managed through event system
- No direct coupling with core game mechanics

### Board Integration  
- SpecialBlockController provides abstraction layer
- No direct board references in bomb implementation
- Coordinate system handled through controller

### Animation Integration
- Safe animation cleanup through AnimationController
- VFX synchronization through dedicated state
- Screen shake and particle effects coordinated

## Dependencies

### Assembly References
- `Whatwapp.MergeSolitaire.Game.asmdef`: Core game systems
- `Tasks.NewFeatures.asmdef`: Special block implementations
- Unity core assemblies (UnityEngine, etc.)

### External Dependencies
- DOTween: Animation and tweening
- Unity Events: Event system foundation
- Unity Coroutines: Timing and async operations

## Design Patterns Used

1. **Interface Segregation**: `ISpecialBlock` defines minimal contract
2. **Factory Pattern**: `BlockFactory` creates appropriate block types
3. **Observer Pattern**: Event system for loose coupling
4. **Singleton Pattern**: `SpecialBlockController` for centralized access
5. **State Pattern**: `VFXState` for game state management
6. **Strategy Pattern**: Different block behaviors through interfaces

## Benefits

### Modularity
- Special blocks can be added without modifying core systems
- Event-driven architecture allows independent feature development
- Clear separation of concerns between systems

### Extensibility  
- New special block types can implement `ISpecialBlock`
- Additional events can be added for new features
- VFX system can handle multiple effect types

### Maintainability
- Centralized controller reduces coupling
- Event system provides clear communication contracts
- Debug modes and logging for troubleshooting

### Performance
- Event system is lightweight and type-safe
- Efficient radius-based calculations
- Smart timing reduces unnecessary processing

---

## Code Quality Analysis & Cleaning Suggestions

### 1. BombBlock Class Improvements

#### Issues Identified:
- **Temporary Debug Logs**: Lines 38, 66 contain temporary debug statements
- **Magic Numbers**: Hard-coded delay values (0.2f, 0.5f)
- **Null Reference Risk**: Multiple `SpecialBlockController.Instance` calls without caching
- **Method Complexity**: `Explode()` method is doing too many things

#### Recommended Fixes:

```csharp
// Remove temporary debug logs
// Line 38: Debug.Log("[BombBlock] OnPlacementComplete called!"); // DELETE
// Line 66: Debug.Log("[BombBlock] OnMovementStopped called!"); // DELETE

// Extract constants
private const float SELF_DESTRUCT_DELAY = 0.2f;
private const float DEFAULT_EXPLOSION_DELAY = 0.5f;

// Cache controller reference
private SpecialBlockController _controller;

private void Awake()
{
    _controller = SpecialBlockController.Instance;
}

// Split Explode() method
private void Explode()
{
    if (_hasExploded) return;
    _hasExploded = true;

    var explosionData = PrepareExplosionData();
    BroadcastExplosionEvents(explosionData);
    StartCoroutine(SelfDestruct());
}

private ExplosionData PrepareExplosionData()
{
    var currentPos = GetCurrentCellPosition();
    var blocksToDestroy = _controller?.GetDestroyableBlocksInRadius(currentPos, _explosionRadius, this)
                         ?? new List<Block>();

    return new ExplosionData
    {
        Position = currentPos,
        BlocksToDestroy = blocksToDestroy,
        BlockPositions = blocksToDestroy.Select(b => b.transform.position).ToList()
    };
}
```

### 2. SpecialBlockController Improvements

#### Issues Identified:
- **Singleton Anti-pattern**: Using `DontDestroyOnLoad` unnecessarily
- **FindObjectOfType Performance**: Called in `OnValidate` and `Awake`
- **Inconsistent Error Handling**: Some methods return null, others log errors
- **Large Class**: Multiple responsibilities in single class

#### Recommended Fixes:

```csharp
// Remove DontDestroyOnLoad - not needed for game-scoped singleton
private void Awake()
{
    if (_instance == null)
    {
        _instance = this;
        InitializeController();
    }
    else if (_instance != this)
    {
        Destroy(gameObject);
    }
}

// Cache board reference properly
private void InitializeController()
{
    if (_board == null)
    {
        _board = FindObjectOfType<Board>();
        if (_board == null)
        {
            Debug.LogError("[SpecialBlockController] No Board found in scene!");
        }
    }

    SubscribeToEvents();
}

// Consistent error handling with Result pattern
public Result<List<Cell>> GetCellsInRadius(Vector2Int centerPos, int radius, bool excludeCenter = true)
{
    if (_board == null)
        return Result<List<Cell>>.Failure("Board reference is null");

    var cells = new List<Cell>();
    // ... implementation
    return Result<List<Cell>>.Success(cells);
}
```

### 3. VFXState Improvements

#### Issues Identified:
- **Complex State Management**: Multiple boolean flags for state tracking
- **Coroutine Management**: Potential memory leaks with coroutine references
- **Magic Numbers**: Hard-coded timing values
- **Unclear Responsibilities**: Mixing VFX and game state logic

#### Recommended Fixes:

```csharp
// Use enum for clearer state management
private enum VFXProcessingState
{
    Idle,
    WaitingForEvents,
    ProcessingEffects,
    Completed
}

// Extract timing constants
private const float VFX_TIMEOUT_DURATION = 0.5f;
private const float SUB_EXPLOSION_DELAY = 0.1f;
private const float SUB_EXPLOSION_SPREAD = 0.05f;
private const float EXTRA_BUFFER_PER_PARTICLE = 0.05f;

// Simplified state management
private VFXProcessingState _processingState = VFXProcessingState.Idle;

public bool CanTransition() => _processingState == VFXProcessingState.Completed;
```

### 4. Event System Improvements

#### Issues Identified:
- **Event Proliferation**: Too many similar events
- **Large Event Structs**: Some events carry unnecessary data
- **No Event Validation**: Events can be broadcast with invalid data

#### Recommended Fixes:

```csharp
// Consolidate related events
public struct BombLifecycleEvent
{
    public BombEventType EventType;
    public BombBlock Source;
    public Vector2Int Position;
    public BombEventData Data;
}

public enum BombEventType
{
    Placed,
    MovementStopped,
    Exploded,
    Destroyed
}

// Add validation
public static class EventValidator
{
    public static bool IsValid(BombExplosionEvent eventData)
    {
        return eventData.SourceBomb != null &&
               eventData.ExplosionRadius > 0 &&
               eventData.DestroyedBlocks != null;
    }
}
```

### 5. BlockFactory Improvements

#### Issues Identified:
- **Inconsistent Initialization**: Bomb blocks don't call `Init()` properly
- **Missing Error Handling**: No fallback if prefab is null
- **Parameter Unused**: `value` and `seed` parameters ignored in `CreateBombBlock`

#### Recommended Fixes:

```csharp
public Block CreateBombBlock(BlockValue value = BlockValue.Ace, BlockSeed seed = BlockSeed.Spades)
{
    if (_bombBlockPrefab == null)
    {
        Debug.LogError("[BlockFactory] Bomb block prefab is not assigned! Falling back to normal block.");
        return Create(value, seed); // Fallback to normal block
    }

    var bombBlock = Instantiate(_bombBlockPrefab, this.transform);
    bombBlock.Init(value, seed); // Actually initialize the bomb block
    return bombBlock;
}
```

### 6. General Architecture Improvements

#### Suggested Refactoring:

1. **Extract Interfaces**:
   ```csharp
   public interface IBoardService
   {
       Vector2Int GetCellCoordinates(Vector3 worldPosition);
       Cell GetCell(Vector2Int coordinates);
       List<Cell> GetCellsInRadius(Vector2Int center, int radius);
   }
   ```

2. **Use Dependency Injection**:
   ```csharp
   public class BombBlock : Block, ISpecialBlock
   {
       private IBoardService _boardService;
       private IEventBus _eventBus;

       public void Initialize(IBoardService boardService, IEventBus eventBus)
       {
           _boardService = boardService;
           _eventBus = eventBus;
       }
   }
   ```

3. **Add Configuration Objects**:
   ```csharp
   [CreateAssetMenu(fileName = "BombConfig", menuName = "Game/Bomb Configuration")]
   public class BombConfiguration : ScriptableObject
   {
       [SerializeField] private float explosionDelay = 0.5f;
       [SerializeField] private int explosionRadius = 1;
       [SerializeField] private bool destroysSelf = true;

       // Properties and validation
   }
   ```

### 7. Performance Optimizations

1. **Object Pooling**: Implement pooling for bomb blocks and explosion effects
2. **Event Batching**: Batch multiple destruction events
3. **Spatial Partitioning**: Use spatial data structures for radius queries
4. **Async Operations**: Use async/await for non-critical operations

### 8. Testing Improvements

1. **Unit Tests**: Add tests for bomb logic, explosion calculations
2. **Integration Tests**: Test event flow and system interactions
3. **Mock Objects**: Create mocks for board and controller dependencies

### Priority Recommendations

**High Priority**:
1. Remove temporary debug logs
2. Fix bomb block initialization in factory
3. Add null reference protection
4. Extract magic numbers to constants

**Medium Priority**:
1. Refactor large methods (Explode, ProcessBombVFXCoroutine)
2. Improve error handling consistency
3. Simplify state management in VFXState

**Low Priority**:
1. Implement dependency injection
2. Add comprehensive unit tests
3. Consider object pooling for performance

---

*This analysis provides actionable suggestions for improving code quality, maintainability, and performance of the Bomb Block system.*
